# 部署指南

## 环境配置

项目支持三种环境的部署：

### 1. 开发环境 (Development)
- **配置文件**: `.env.development`
- **Docker Compose**: `docker-compose.yml`
- **API地址**: `http://localhost:3000/api`
- **前端端口**: `5173`

### 2. 测试环境 (Test)
- **配置文件**: `.env.test`
- **Docker Compose**: `docker-compose.test.yml`
- **API地址**: `https://test.moodplay.top/api`
- **前端端口**: `5173`

### 3. 生产环境 (Production)
- **配置文件**: `.env.production`
- **Docker Compose**: `docker-compose.prod.yml`
- **API地址**: `https://moodplay.top/api`
- **前端端口**: `5173` (映射到容器内的80端口)

## 前端环境配置

前端使用 Vite 构建工具，支持不同环境的配置：

- **开发环境**: 使用 `yarn dev` 命令，自动加载 `.env.development`
- **测试环境**: 使用 `yarn dev:test` 命令，加载 `.env.test`
- **生产环境**: 使用 `yarn build` 命令构建静态文件，加载 `.env.production`

## 部署方式

### 本地部署

#### 开发环境
```bash
docker compose up -d
```

#### 测试环境
```bash
docker compose --env-file .env.test -f docker-compose.test.yml up -d
```

#### 生产环境
```bash
docker compose --env-file .env.production -f docker-compose.prod.yml up -d
```

### 脚本部署

#### 测试环境
```bash
./scripts/deploy-test.sh
# 或指定分支
./scripts/deploy-test.sh -b feature-branch
```

#### 生产环境
```bash
./scripts/deploy-prod.sh
# 或指定分支
./scripts/deploy-prod.sh -b release-branch
```

### 自动部署 (GitHub Actions)

- **测试环境**: 推送到 `develop` 分支时自动部署
- **生产环境**: 推送到 `main` 分支时自动部署

## 前端构建说明

### 开发模式
- 使用 Vite 开发服务器
- 支持热重载
- 挂载源代码目录

### 生产模式
- 使用多阶段 Docker 构建
- 构建静态文件
- 使用 Nginx 服务器提供服务
- 优化缓存和压缩

## 环境变量说明

### 前端环境变量
- `VITE_API_BASE_URL`: 后端API地址
- `VITE_APP_ENV`: 当前环境标识
- `VITE_APP_NAME`: 应用名称

### 注意事项
1. 确保每个环境都有对应的 `.env` 文件
2. 生产环境使用静态文件部署，性能更好
3. 测试环境仍使用开发模式，便于调试
4. 所有环境变量以 `VITE_` 开头才能在前端访问
