#!/bin/bash

echo "=== 环境变量测试脚本 ==="
echo "当前工作目录: $(pwd)"
echo "脚本路径: ${BASH_SOURCE[0]}"
echo ""

echo "=== 检查 .env 文件 ==="
if [ -f ".env" ]; then
    echo "✓ .env 文件存在"
    echo "文件大小: $(wc -c < .env) 字节"
    echo "文件行数: $(wc -l < .env) 行"
    echo ""
    echo ".env 文件内容："
    cat -n .env
    echo ""
else
    echo "✗ .env 文件不存在"
    echo "当前目录文件列表："
    ls -la
    exit 1
fi

echo "=== 加载前的环境变量 ==="
env | grep -E "^DB_|^MYSQL_" || echo "没有找到 DB_ 或 MYSQL_ 开头的环境变量"
echo ""

echo "=== 尝试加载 .env 文件 ==="
source .env
echo "source .env 执行完成，返回码: $?"
echo ""

echo "=== 加载后的环境变量 ==="
echo "DB_USER=$DB_USER"
echo "DB_PASSWORD=${DB_PASSWORD:+[已设置，长度: ${#DB_PASSWORD}]}"
echo "DB_HOST=$DB_HOST"
echo "DB_NAME=$DB_NAME"
echo "DB_PORT=$DB_PORT"
echo ""

echo "=== 所有 DB_ 开头的环境变量 ==="
env | grep "^DB_" || echo "没有找到 DB_ 开头的环境变量"
echo ""

echo "=== 测试变量是否为空 ==="
if [ -z "$DB_PASSWORD" ]; then
    echo "✗ DB_PASSWORD 为空或未设置"
else
    echo "✓ DB_PASSWORD 已设置"
fi

echo ""
echo "=== 测试完成 ==="
