import { useState, useEffect } from 'react'
import { ArrowLeft, Settings, Save, AlertCircle, CheckCircle, Loader2 } from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Switch } from './ui/switch'
import { Textarea } from './ui/textarea'
import { Label } from './ui/label'
import { Alert, AlertDescription } from './ui/alert'
import { apiService } from '../services/api'

interface SystemConfigManagerProps {
  onBackToPages: () => void
}

interface SystemStatus {
  isAvailable: boolean
  message: string
}

export function SystemConfigManager({ onBackToPages }: SystemConfigManagerProps) {
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    isAvailable: true,
    message: ''
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  // 加载系统状态
  useEffect(() => {
    loadSystemStatus()
  }, [])

  const loadSystemStatus = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await apiService.getSystemStatus()
      if (response.success && response.data) {
        setSystemStatus({
          isAvailable: response.data.isAvailable,
          message: response.data.message || ''
        })
      } else {
        throw new Error(response.message || '获取系统状态失败')
      }
    } catch (error) {
      console.error('Failed to load system status:', error)
      setError(error instanceof Error ? error.message : '获取系统状态失败')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      setError(null)
      setSuccess(null)

      // 验证：如果系统不可用，必须提供提示消息
      if (!systemStatus.isAvailable && !systemStatus.message.trim()) {
        setError('系统不可用时必须提供提示消息')
        return
      }

      const response = await apiService.updateSystemStatus(
        systemStatus.isAvailable,
        systemStatus.message.trim()
      )

      if (response.success) {
        setSuccess('系统状态更新成功')
        // 3秒后清除成功消息
        setTimeout(() => setSuccess(null), 3000)
      } else {
        throw new Error(response.message || '更新系统状态失败')
      }
    } catch (error) {
      console.error('Failed to update system status:', error)
      setError(error instanceof Error ? error.message : '更新系统状态失败')
    } finally {
      setSaving(false)
    }
  }

  const handleToggleChange = (checked: boolean) => {
    setSystemStatus(prev => ({
      ...prev,
      isAvailable: checked,
      // 如果切换到可用状态，清空消息
      message: checked ? '' : prev.message
    }))
  }

  const handleMessageChange = (value: string) => {
    setSystemStatus(prev => ({
      ...prev,
      message: value
    }))
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg mb-4">
            <Loader2 className="w-8 h-8 text-white animate-spin" />
          </div>
          <p className="text-gray-600">正在加载系统配置...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 装饰性背景 */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KPGcgZmlsbD0iIzAwMDAwMCIgZmlsbC1vcGFjaXR5PSIwLjAzIj4KPGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMiIvPgo8L2c+CjwvZz4KPC9zdmc+')] opacity-40"></div>
      
      <div className="relative z-10 p-8">
        <div className="max-w-4xl mx-auto">
          {/* 头部 */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={onBackToPages}
                className="flex items-center space-x-2 hover:bg-white/80"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回</span>
              </Button>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                  <Settings className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">系统配置</h1>
                  <p className="text-sm text-gray-500">管理系统可用性状态和提示消息</p>
                </div>
              </div>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert className="mb-6 border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* 成功提示 */}
          {success && (
            <Alert className="mb-6 border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                {success}
              </AlertDescription>
            </Alert>
          )}

          {/* 配置卡片 */}
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-6">
              <CardTitle className="text-xl text-gray-900">系统可用性控制</CardTitle>
              <CardDescription className="text-gray-600">
                控制小程序是否可以正常访问后端服务，以及不可用时的提示消息
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 系统状态开关 */}
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="space-y-1">
                  <Label className="text-base font-medium text-gray-900">
                    系统状态
                  </Label>
                  <p className="text-sm text-gray-600">
                    {systemStatus.isAvailable ? '系统当前可用，小程序可以正常访问' : '系统当前不可用，小程序将显示维护提示'}
                  </p>
                </div>
                <Switch
                  checked={systemStatus.isAvailable}
                  onCheckedChange={handleToggleChange}
                  className="data-[state=checked]:bg-green-500"
                />
              </div>

              {/* 提示消息配置 */}
              {!systemStatus.isAvailable && (
                <div className="space-y-3">
                  <Label htmlFor="message" className="text-base font-medium text-gray-900">
                    不可用提示消息 <span className="text-red-500">*</span>
                  </Label>
                  <Textarea
                    id="message"
                    placeholder="请输入系统不可用时显示给用户的提示消息..."
                    value={systemStatus.message}
                    onChange={(e) => handleMessageChange(e.target.value)}
                    className="min-h-[100px] resize-none"
                    maxLength={200}
                  />
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>系统不可用时将显示此消息给用户</span>
                    <span>{systemStatus.message.length}/200</span>
                  </div>
                </div>
              )}

              {/* 保存按钮 */}
              <div className="flex justify-end pt-4">
                <Button
                  onClick={handleSave}
                  disabled={saving || (!systemStatus.isAvailable && !systemStatus.message.trim())}
                  className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-8"
                >
                  {saving ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      保存中...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      保存配置
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

        </div>
      </div>
    </div>
  )
}
