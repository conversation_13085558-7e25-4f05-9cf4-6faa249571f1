import { useState, useEffect } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './components/ui/select'
import { Settings, LogOut, User } from 'lucide-react'
import { ConfigManager } from './components/ConfigManager'
import { OnboardingConfigManager } from './components/OnboardingConfigManager'
import { EndGoalConfigManager } from './components/EndGoalConfigManager'
import { SystemConfigManager } from './components/SystemConfigManager'
import { UserRewardManager } from './components/UserRewardManager'
import LoginForm from './components/LoginForm'
import { apiService, authUtils } from './services/api'
import './App.css'

const pages = [
  {
    id: 'tap-talk',
    name: 'Tap Talk 页面',
    description: '管理tap talk 页面的内容配置',
    icon: '📱'
  },
  {
    id: 'onboarding',
    name: 'Onboarding 页面',
    description: '管理引导页面的内容配置',
    icon: '🎬'
  },
  {
    id: 'end-goal',
    name: '频道页面',
    description: '管理频道页面的内容配置',
    icon: '🎯'
  },
  {
    id: 'system-config',
    name: '系统配置',
    description: '管理系统可用性状态和维护提示',
    icon: '⚙️'
  },
  {
    id: 'user-reward',
    name: '用户奖励',
    description: '批量发放用户会员奖励',
    icon: '🎁'
  }
]

interface User {
  username: string
  role: string
}

function App() {
  const [selectedPageId, setSelectedPageId] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [authLoading, setAuthLoading] = useState(true)
  const [loginLoading, setLoginLoading] = useState(false)

  // 检查认证状态
  useEffect(() => {
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    try {
      if (!authUtils.isAuthenticated()) {
        setAuthLoading(false)
        return
      }

      const response = await apiService.verifyToken()
      if (response.success && response.data) {
        setIsAuthenticated(true)
        setUser({
          username: response.data.username,
          role: response.data.role
        })
      } else {
        // token无效，清除本地存储
        authUtils.removeToken()
        setIsAuthenticated(false)
        setUser(null)
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      // token无效，清除本地存储
      authUtils.removeToken()
      setIsAuthenticated(false)
      setUser(null)
    } finally {
      setAuthLoading(false)
    }
  }

  const handleLogin = async (username: string, password: string) => {
    setLoginLoading(true)
    try {
      const response = await apiService.login(username, password)
      if (response.success && response.data) {
        authUtils.saveToken(response.data.token)
        setIsAuthenticated(true)
        setUser({
          username: response.data.username,
          role: response.data.role
        })
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      throw error
    } finally {
      setLoginLoading(false)
    }
  }

  const handleLogout = () => {
    authUtils.removeToken()
    setIsAuthenticated(false)
    setUser(null)
    setSelectedPageId(null)
  }

  const handlePageSelect = (pageId: string) => {
    setLoading(true)
    // 模拟加载延迟
    setTimeout(() => {
      setSelectedPageId(pageId)
      setLoading(false)
    }, 300)
  }

  const handleBackToPages = () => {
    setSelectedPageId(null)
  }

  // 认证状态加载中
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg mb-4">
            <Settings className="w-8 h-8 text-white animate-spin" />
          </div>
          <p className="text-gray-600">正在验证登录状态...</p>
        </div>
      </div>
    )
  }

  // 未认证，显示登录页面
  if (!isAuthenticated) {
    return <LoginForm onLogin={handleLogin} isLoading={loginLoading} />
  }

  // 已认证，根据选择的页面渲染对应的组件
  if (selectedPageId) {
    switch (selectedPageId) {
      case 'tap-talk':
        return <ConfigManager onBackToPages={handleBackToPages} />
      case 'onboarding':
        return <OnboardingConfigManager onBackToPages={handleBackToPages} />
      case 'end-goal':
        return <EndGoalConfigManager onBackToPages={handleBackToPages} />
      case 'system-config':
        return <SystemConfigManager onBackToPages={handleBackToPages} />
      case 'user-reward':
        return <UserRewardManager onBackToPages={handleBackToPages} />
      default:
        return null
    }
  }

  // 页面选择界面
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 装饰性背景 */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KPGcgZmlsbD0iIzAwMDAwMCIgZmlsbC1vcGFjaXR5PSIwLjAzIj4KPGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMiIvPgo8L2c+CjwvZz4KPC9zdmc+')] opacity-40"></div>
      
      <div className="relative z-10 p-8">
        <div className="max-w-5xl mx-auto">
          {/* 用户信息和登出按钮 */}
          <div className="flex justify-end mb-6">
            <div className="flex items-center space-x-4 bg-white/80 backdrop-blur-sm rounded-lg px-4 py-2 shadow-lg">
              <div className="flex items-center space-x-2">
                <User className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-700">欢迎，{user?.username}</span>
              </div>
              <button
                onClick={handleLogout}
                className="flex items-center space-x-1 px-3 py-1 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
              >
                <LogOut className="w-4 h-4" />
                <span>退出登录</span>
              </button>
            </div>
          </div>

          {/* 页面标题区域 */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-xl mb-6">
              <Settings className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-900 bg-clip-text text-transparent mb-4">
              MoodPlay 配置管理后台
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
              欢迎使用 MoodPlay 配置管理系统，在这里您可以管理小程序的页面配置
            </p>
          </div>

          {/* 选择页面卡片 */}
          <div className="max-w-2xl mx-auto">
            <Select onValueChange={handlePageSelect} disabled={loading}>
              <SelectTrigger className="h-16 text-lg border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500 rounded-xl transition-all duration-200">
                <SelectValue 
                  placeholder="🎯 请选择页面..." 
                  className="text-gray-500"
                />
              </SelectTrigger>
              <SelectContent className="rounded-xl border-0 shadow-2xl max-h-80">
                {pages.map(page => (
                  <SelectItem 
                    key={page.id} 
                    value={page.id}
                    className="p-4 rounded-lg m-1 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 cursor-pointer transition-all duration-200 focus:bg-gradient-to-r focus:from-blue-50 focus:to-indigo-50"
                  >
                    <div className="flex items-center w-full">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                        <span className="text-white text-xl">{page.icon}</span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-semibold text-gray-900 text-base">{page.name}</div>
                        <div className="text-sm text-gray-500 mt-1 leading-tight">{page.description}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    </div>
  )
}

export default App
