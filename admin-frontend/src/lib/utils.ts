import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import type { PageConfig, Page } from "../types/config"
import type { OnboardingConfig } from "../types/onboarding"
import type { EndGoalConfig } from "../types/end-goal"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Mock数据
export const mockPages: Page[] = [
  {
    id: 'tap-talk',
    name: 'Tap Talk',
    description: '微醺正念页面配置'
  },
  {
    id: 'onboarding',
    name: 'Onboarding',
    description: '引导页面配置'
  },
  {
    id: 'end-goal',
    name: 'End Goal',
    description: '调节方案选择页面配置'
  },
  {
    id: 'meditation',
    name: 'Meditation',
    description: '冥想页面配置'
  },
  {
    id: 'sleep-story',
    name: 'Sleep Story',
    description: '睡前故事页面配置'
  }
];

export const mockTapTalkConfig: PageConfig = {
  id: 'tap-talk',
  name: 'Tap Talk',
  navbarTitle: "微醺正念",
  bottomIcon: "https://test-cdn.moodplay.top/assets/pages/tap-talk/bottom-icon.svg",
  nextIcon: "https://test-cdn.moodplay.top/assets/pages/tap-talk/next-icon.svg",
  enterIcon: "https://test-cdn.moodplay.top/assets/pages/tap-talk/enter-icon.svg",
  leaveButtonText: "离开",
  cancelButtonText: "返回",
  leavingConfirmText: "确认离开微醺正念?",
  glichyImage: "assets/pages/tap-talk/glichy-image.png",
  overlayImage: "assets/pages/tap-talk/overlay-image.png",
  audioPlayingIcon: "assets/pages/tap-talk/audio-playing.svg",
  audioPausedIcon: "assets/pages/tap-talk/audio-paused.svg",
  tapTalks: [
    {
      category: "night",
      description: "夜间前奏",
      buttonText: "好耶！晚安",
      audios: [
        "assets/pages/tap-talk/night/SpotiMate.io - Unfolding - 4 AM Edit - Manolakas.mp3",
        "assets/pages/tap-talk/night/SpotiMate.io - Equanimity - Hank Sky.mp3",
        "assets/pages/tap-talk/night/SpotiMate.io - Northwest - Waveframe.mp3",
        "assets/pages/tap-talk/night/SpotiMate.io - Dream _1 _Arrival_ - charlie dreaming.mp3",
        "assets/pages/tap-talk/night/SpotiMate.io - Dreaming of You - Calmative.mp3",
        "assets/pages/tap-talk/night/SpotiMate.io - Meas - Nolene Britton.mp3",
        "assets/pages/tap-talk/night/SpotiMate.io - Brionglóid _Sound Bath_ - Tamila Gagan.mp3"
      ],
      items: [
        {
          id: 1,
          name: "云朵呼吸",
          image: "https://test-cdn.moodplay.top/assets/pages/tap-talk/night/night-image1.png",
          detailImage: "assets/pages/tap-talk/night/night-detail-bg1.png",
          script: "assets/pages/tap-talk/night/night-script1.json"
        },
        {
          id: 2,
          name: "睡前三件事",
          image: "https://test-cdn.moodplay.top/assets/pages/tap-talk/night/night-image2.png",
          detailImage: "assets/pages/tap-talk/night/night-detail-bg2.png",
          script: "assets/pages/tap-talk/night/night-script2.json"
        },
        {
          id: 3,
          name: "关掉感官",
          image: "https://test-cdn.moodplay.top/assets/pages/tap-talk/night/night-image3.png",
          detailImage: "assets/pages/tap-talk/night/night-detail-bg3.png",
          script: "assets/pages/tap-talk/night/night-script3.json"
        }
      ]
    },
    {
      category: "daylight",
      description: "日间重启",
      buttonText: "好耶！击掌",
      audios: [
        "assets/pages/tap-talk/daylight/SpotiMate.io - Summer Memories - Gavin Luke.mp3",
        "assets/pages/tap-talk/daylight/SpotiMate.io - Dragonfly - Ambient Summer Version - Richard Goldsworthy.mp3",
        "assets/pages/tap-talk/daylight/SpotiMate.io - Muddus - Laponia.mp3",
        "assets/pages/tap-talk/daylight/SpotiMate.io - Illumination of the Heart - Deuter.mp3",
        "assets/pages/tap-talk/daylight/SpotiMate.io - Bright Belief - Jonny Nash.mp3",
        "assets/pages/tap-talk/daylight/SpotiMate.io - Lysa - Liva.mp3",
        "assets/pages/tap-talk/daylight/SpotiMate.io - Them - Nils Frahm.mp3"
      ],
      items: [
        {
          id: 1,
          name: "为今天设定一个关键词",
          image: "https://test-cdn.moodplay.top/assets/pages/tap-talk/daylight/daylight-image1.png",
          detailImage: "assets/pages/tap-talk/daylight/daylight-detail-bg1.png",
          script: "assets/pages/tap-talk/daylight/daylight-script1.json"
        },
        {
          id: 2,
          name: "信号消失中",
          image: "https://test-cdn.moodplay.top/assets/pages/tap-talk/daylight/daylight-image2.png",
          detailImage: "assets/pages/tap-talk/daylight/daylight-detail-bg2.png",
          script: "assets/pages/tap-talk/daylight/daylight-script2.json"
        },
        {
          id: 3,
          name: "晨间唤醒咖啡因",
          image: "https://test-cdn.moodplay.top/assets/pages/tap-talk/daylight/daylight-image3.png",
          detailImage: "assets/pages/tap-talk/daylight/daylight-detail-bg3.png",
          script: "assets/pages/tap-talk/daylight/daylight-script3.json"
        }
      ]
    },
    {
      category: "mantra",
      description: "显化咒语",
      buttonText: "好耶！一定",
      audios: [
        "assets/pages/tap-talk/mantra/SpotiMate.io - aashaavaan - Kanta Khatri.mp3",
        "assets/pages/tap-talk/mantra/SpotiMate.io - Balsamo - Slowdonia.mp3",
        "assets/pages/tap-talk/mantra/SpotiMate.io - Focus Three - Yoro Kobuto.mp3",
        "assets/pages/tap-talk/mantra/SpotiMate.io - Gaia Awakening - Syntropy.mp3",
        "assets/pages/tap-talk/mantra/SpotiMate.io - Mukti Tabla - LaYam.mp3",
        "assets/pages/tap-talk/mantra/SpotiMate.io - Underwater Temples of Mahabalipuram - The Golden Islands.mp3"
      ],
      items: [
        {
          id: 1,
          name: "有如神力咒语",
          image: "https://test-cdn.moodplay.top/assets/pages/tap-talk/mantra/mantra-image1.png",
          detailImage: "assets/pages/tap-talk/mantra/mantra-detail-bg1.png",
          script: "assets/pages/tap-talk/mantra/mantra-script1.json"
        },
        {
          id: 2,
          name: "我允许",
          image: "https://test-cdn.moodplay.top/assets/pages/tap-talk/mantra/mantra-image2.png",
          detailImage: "assets/pages/tap-talk/mantra/mantra-detail-bg2.png",
          script: "assets/pages/tap-talk/mantra/mantra-script2.json"
        }
      ]
    },
    {
      category: "mini-spa",
      description: "身心泡泡浴",
      buttonText: "好耶！吹泡泡",
      audios: [
        "assets/pages/tap-talk/minispa/SpotiDown.App - Days of Summer - Open Road Folk Music.mp3",
        "assets/pages/tap-talk/minispa/SpotiDown.App - Ferns and I - Loris S. Sarid.mp3",
        "assets/pages/tap-talk/minispa/SpotiDown.App - Shining Sound - Milkyway Outcast_ Koshi Chimes.mp3",
        "assets/pages/tap-talk/minispa/SpotiMate.io - Botanique - J Foerster_ N KRAMER.mp3",
        "assets/pages/tap-talk/minispa/SpotiMate.io - Ontspanning - Femke Edelijn.mp3",
        "assets/pages/tap-talk/minispa/SpotiMate.io - Rain - Green-House.mp3",
        "assets/pages/tap-talk/minispa/SpotiMate.io - Wint3R - Wint3r.mp3"
      ],
      items: [
        {
          id: 1,
          name: "小行动魔法",
          image: "https://test-cdn.moodplay.top/assets/pages/tap-talk/minispa/mini-spa-image1.png",
          detailImage: "assets/pages/tap-talk/minispa/minispa-detail-bg1.png",
          script: "assets/pages/tap-talk/minispa/mini-spa-script1.json"
        },
        {
          id: 2,
          name: "小猫呼吸",
          image: "https://test-cdn.moodplay.top/assets/pages/tap-talk/minispa/mini-spa-image2.png",
          detailImage: "assets/pages/tap-talk/minispa/minispa-detail-bg2.png",
          script: "assets/pages/tap-talk/minispa/mini-spa-script2.json"
        }
      ]
    }
  ]
};

export const mockOnboardingConfig: OnboardingConfig = {
  buttonText: "进入",
  entryText1: "欢迎来到 moodplay！",
  entryText2: "先花1分钟，认识一下我们，\n帮你安心开启情绪调频旅程:)",
  startText1: " 开始 moodplay 的\n第一段陪伴",
  startText2: "首次登录领取7天免费会员\n快来解锁moodplay 的全部功能吧",
  startButtonText: "登录",
  startImage: "assets/pages/onboarding/start-image.png?v=************",
  logoIcon: "assets/pages/onboarding/logo-icon.svg",
  steps: [
    {
      text1: "可以输入\n任何当下的状态",
      text2: "我们欢迎一切你的声音：当下的情绪、最近的困扰、思路的卡顿，甚至是怎么过好第二人生",
      image: "assets/pages/onboarding/step1-image.png?v=************"
    },
    {
      text1: "moodplay\n会为你生成专属的声音引导",
      text2: "10 分钟，从你输入的状态出发，生成专属的声音引导。帮你松下来、想明白、或者重新出发",
      image: "assets/pages/onboarding/step2-image.png?v=************"
    },
    {
      text1: "独创情绪调频体系\n支持你的内在转化",
      text2: "结合正念实践、心理学与新时代疗愈方法，我们构建出六大情绪调频时刻，不缺席你的每一次波动",
      image: "assets/pages/onboarding/step3-image.png?v=************"
    },
    {
      text1: "每次输入\n都匿名安心",
      text2: "我们尊重每一次表达的边界与私密性。\n我们承诺你的输入不会被分析、追踪，或用于任何未经同意的用途",
      image: "assets/pages/onboarding/step4-image.png?v=************"
    }
  ],
  backgroundVideo: "assets/pages/onboarding/background-video.mp4?v=************",
  soundSetting: {
    rippleGif: "assets/pages/onboarding/sound-setting/ripple.gif",
    rippleIcon: "assets/pages/onboarding/sound-setting/ripple.svg",
    buttonText: "选择",
    guideText: "选择\n你最有感觉的声音",
    auditionText: "试听",
    auditionIcon: "assets/pages/onboarding/sound-setting/audition-icon.svg",
    auditionPauseIcon: "assets/pages/onboarding/sound-setting/audition-pause-icon.svg",
    leftArrowDarkIcon: "assets/pages/onboarding/sound-setting/left-arrow-dark-icon.svg",
    leftArrowIcon: "assets/pages/onboarding/sound-setting/left-arrow-light-icon.svg",
    rightArrowDarkIcon: "assets/pages/onboarding/sound-setting/right-arrow-dark-icon.svg",
    rightArrowIcon: "assets/pages/onboarding/sound-setting/right-arrow-light-icon.svg",
    playingGif: "assets/pages/onboarding/sound-setting/playing.gif",
    playingIcon: "assets/pages/onboarding/sound-setting/playing-icon.svg",
    figures: [
      {
        id: 1,
        refId: "moodplay_River_v1",
        name: "River",
        avatarImage: "assets/pages/onboarding/sound-setting/avatar1-image.png",
        soundDemo: "assets/pages/onboarding/sound-setting/sound-demo-River.mp3",
        introduce1: "受训于哈佛医学院正念中心（CMI），\n也是爱大自然的心理咨询师\n将自然节律融入身心觉察，让身自在、心自由",
        isDefault: true
      },
      {
        id: 2,
        refId: "moodplay_Tara_v1",
        name: "Tara",
        avatarImage: "assets/pages/onboarding/sound-setting/avatar2-image.png",
        soundDemo: "assets/pages/onboarding/sound-setting/sound-demo-Tara.mp3",
        introduce1: "冥想十年，Co-Active 教练，\n也是四岁孩子的妈妈与连续创业者\n习惯在多重角色中切换，\n也从不忘给'爱自己'留出空间",
        isDefault: false
      },
      {
        id: 3,
        refId: "moodplay_Kai_v1",
        name: "Kai",
        avatarImage: "assets/pages/onboarding/sound-setting/avatar3-image.png",
        soundDemo: "assets/pages/onboarding/sound-setting/sound-demo-Kai.mp3",
        introduce1: "念哲学，做金融，也开健身房\n把世界当做游乐场\n不给自己设限，总在探索世界更广阔的可能",
        isDefault: false
      },
      {
        id: 4,
        refId: "moodplay_Diane_v1",
        name: "Diane",
        avatarImage: "assets/pages/onboarding/sound-setting/avatar4-image.png",
        soundDemo: "assets/pages/onboarding/sound-setting/sound-demo-Diane.mp3",
        introduce1: "曾是大学讲师与外交官，奔走世界\n如今将脚步落在瑜伽、太极与音疗，\n把修行过成生活",
        isDefault: false
      },
      {
        id: 5,
        refId: "moodplay_Yizhou_v1",
        name: "Yizhou",
        avatarImage: "assets/pages/onboarding/sound-setting/avatar5-image.png",
        soundDemo: "assets/pages/onboarding/sound-setting/sound-demo-Yizhou.mp3",
        introduce1: "北大与牛津宗教哲学学者，钻研梵语经典\n相信古老智慧中藏着靠近自己、通往永恒的路",
        isDefault: false
      },
      {
        id: 6,
        refId: "moodplay_Wen_v1",
        name: "Wen",
        avatarImage: "assets/pages/onboarding/sound-setting/avatar6-image.png",
        soundDemo: "assets/pages/onboarding/sound-setting/sound-demo-Wen.mp3",
        introduce1: "温柔又有力量的双鱼座，精通塔罗与星盘\n相信宇宙的节奏，也相信每个人都有直觉和智慧，洞察自己的方向",
        isDefault: false
      }
    ]
  }
};

export const mockEndGoalConfig: EndGoalConfig = {
  title: "选择调节方案",
  buttonText: "选择",
  infoIcon: "/assets/pages/end-goal/info-icon.svg",
  maskImage: "/assets/pages/end-goal/mask-image.png",
  goalConfigs: [
    {
      goalKey: "松弛时刻",
      mainColor: "#8CBA70",
      tag: "relax",
      tagIcon: "assets/pages/end-goal/松弛时刻/tag-icon.svg",
      description: "让神经松开，身体轻盈，思绪放慢，回到一种柔软、安静、自然流动的状态。*",
      scenario: "身心紧绷 ｜疲惫难以放松 | 想慢下来却找不到感觉 | 生活节奏快 | 入睡困难等",
      example: "节奏呼吸调整｜可视化放松｜身体扫描｜感官觉察等",
      quote: "* 对应人类基础情绪系统中的 PLAY 系统（参考 Panksepp 情绪神经科学理论），有助于恢复轻盈与愉悦放松状态。"
    },
    {
      goalKey: "释怀时刻",
      mainColor: "#71A9C3",
      tag: "release",
      tagIcon: "assets/pages/end-goal/释怀时刻/tag-icon.svg",
      description: "让情绪有出口，卸下压抑与内耗，为自己腾出空间，把卡住的能量释放出来。*",
      scenario: "心里有点闷、压了一些情绪但说不清、想哭但又绷着、情绪需要个出口等",
      example: "情绪命名与定位 ｜象征性表达 ｜动作释放｜内在空间构建/情绪释放可视化等",
      quote: "* 对应人类基础情绪系统中的RAGE / PANIC 系统（参考 Panksepp 情绪神经科学理论），协助安全地表达并释放强烈情绪。"
    },
    {
      goalKey: "共情时刻",
      mainColor: "#E94855",
      tag: "empathy",
      tagIcon: "assets/pages/end-goal/共情时刻/tag-icon.svg",
      description: "在人际关系里找回理解与连接，让心重新靠近，变得柔软，也更有力量。*",
      scenario: "在关系里不舒服、不知道如何沟通表达、和人相处有点疲惫、渴望被理解和看见、想修复一段关系等",
      example: "视角切换练习｜慈心祝福｜人际关系动力学｜非暴力沟通等",
      quote: "* 对应人类基础情绪系统中的CARE 系统（参考 Panksepp 情绪神经科学理论），支持建立亲密感与共情能力，增强内在联结。"
    },
    {
      goalKey: "稳定时刻",
      mainColor: "#988776",
      tag: "ground",
      tagIcon: "assets/pages/end-goal/稳定时刻/tag-icon.svg",
      description: "让情绪落地、状态归位，找回内在秩序与一种踏实、不慌的稳定感。*",
      scenario: "节奏被打乱、压力大时慌神失焦、想找回自信和掌控感、希望内心更有边界感和定力等",
      example: "身体锚定 ｜自我肯定 ｜价值澄清 ｜ 信念显化等",
      quote: "* 对应人类基础情绪系统中的FEAR / PANIC 系统（参考 Panksepp 情绪神经科学理论），帮助安抚焦虑、重建安全感与边界感。"
    },
    {
      goalKey: "行动时刻",
      mainColor: "#DE6C42",
      tag: "action",
      tagIcon: "assets/pages/end-goal/行动时刻/tag-icon.svg",
      description: "唤起内在动力，突破卡顿与拖延，回到愿意迈出一步、靠近目标的自己。*",
      scenario: "想做点什么但提不起劲、有点拖延、目标模糊、想给自己一个小推动、准备启动一件事等",
      example: "行动拆解 | 动机召回 | 卡点识别 | 动能巩固仪式等",
      quote: "* 对应人类基础情绪系统中的EEKING 系统（参考 Panksepp 情绪神经科学理论），激发探索与前进的欲望，提升动机与行动力。"
    },
    {
      goalKey: "灵光时刻",
      mainColor: "#858CEF",
      tag: "inspire",
      tagIcon: "assets/pages/end-goal/灵光时刻/tag-icon.svg",
      description: "打破固有视角，让思绪流动起来，灵感与方向自然浮现。*",
      scenario: "想换个角度看事情、感觉有点卡住、生活需要一点灵感、想为自己创造点可能性等",
      example: "认知切换练习 | 象征投射 | 问题重构 | 创意思维实验等",
      quote: "* 对应人类基础情绪系统中的SEEKING 系统（参考 Panksepp 情绪神经科学理论），带来对意义与可能性的感知，激发创造性联想。"
    }
  ]
};

 