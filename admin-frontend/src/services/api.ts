const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'

export interface BackendResponse<T = any> {
  code: number
  msg: string
  data?: T
}

export interface StandardResponse<T = any> {
  success: boolean
  message: string
  data?: T
}

export interface LoginResponse {
  success: boolean
  message: string
  data?: {
    token: string
    username: string
    role: string
  }
}

class ApiService {
  private getHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    const token = localStorage.getItem('admin_token')
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    return headers
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<StandardResponse<T>> {
    const url = `${API_BASE_URL}${endpoint}`
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.getHeaders(),
        ...options.headers,
      },
    }

    try {
      const response = await fetch(url, config)
      const data: BackendResponse<T> = await response.json()

      if (!response.ok) {
        // 对于错误响应，后端会返回 {code: 错误码, msg: 错误信息, data: null}
        const errorMessage = data.msg || `HTTP error! status: ${response.status}`
        throw new Error(errorMessage)
      }

      // 将后端格式转换为前端期望格式
      return {
        success: data.code === 0,
        message: data.msg,
        data: data.data
      }
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }

  // 登录
  async login(username: string, password: string): Promise<LoginResponse> {
    return this.request<LoginResponse['data']>('/admin/login', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    })
  }

  // 验证token
  async verifyToken(): Promise<StandardResponse> {
    return this.request('/admin/verify-token', {
      method: 'POST',
    })
  }

  // 获取配置
  async getConfig(pageName: string): Promise<StandardResponse> {
    return this.request(`/admin/config/${pageName}`)
  }

  // 更新配置
  async updateConfig(pageName: string, content: string): Promise<StandardResponse> {
    return this.request(`/admin/config/${pageName}`, {
      method: 'PUT',
      body: JSON.stringify({ content }),
    })
  }

  // 上传资源
  async uploadResource(pageName: string, file: File, config: string): Promise<StandardResponse> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('config', config)

    const token = localStorage.getItem('admin_token')
    const headers: Record<string, string> = {}
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    const response = await fetch(`${API_BASE_URL}/admin/resource/${pageName}/upload`, {
      method: 'POST',
      body: formData,
      headers,
    })

    const data: BackendResponse = await response.json()

    if (!response.ok) {
      // 对于错误响应，后端会返回 {code: 错误码, msg: 错误信息, data: null}
      const errorMessage = data.msg || `HTTP error! status: ${response.status}`
      throw new Error(errorMessage)
    }

    // 将后端格式转换为前端期望格式
    return {
      success: data.code === 0,
      message: data.msg,
      data: data.data
    }
  }

  // 获取脚本内容
  async getScriptContent(scriptPath: string): Promise<StandardResponse> {
    return this.request('/admin/script-content', {
      method: 'POST',
      body: JSON.stringify({ scriptPath }),
    })
  }

  // 方法别名，为了兼容不同的命名习惯

  // 获取页面配置 (别名)
  async getPageConfig(pageName: string): Promise<StandardResponse> {
    return this.getConfig(pageName)
  }

  // 更新页面配置 (别名，支持config对象参数)
  async updatePageConfig(pageName: string, config: any): Promise<StandardResponse> {
    return this.updateConfig(pageName, JSON.stringify(config))
  }

  // 获取系统状态
  async getSystemStatus(): Promise<StandardResponse> {
    return this.request('/admin/system-status')
  }

  // 更新系统状态
  async updateSystemStatus(isAvailable: boolean, message?: string): Promise<StandardResponse> {
    return this.request('/admin/system-status', {
      method: 'PUT',
      body: JSON.stringify({ isAvailable, message }),
    })
  }

  // 上传资源和更新配置 (别名，支持config对象参数)
  async uploadResourceAndUpdateConfig(pageName: string, file: File, config: any): Promise<StandardResponse> {
    return this.uploadResource(pageName, file, JSON.stringify(config))
  }

  // 批量发放用户奖励
  async grantBatchRewards(rewardTypeKey: string, phoneNumbers: string[]): Promise<StandardResponse> {
    return this.request('/admin/grant-batch-rewards', {
      method: 'POST',
      body: JSON.stringify({ rewardTypeKey, phoneNumbers }),
    })
  }
}

export const apiService = new ApiService()

// 认证相关的辅助函数
export const authUtils = {
  // 保存token
  saveToken(token: string): void {
    localStorage.setItem('admin_token', token)
  },

  // 获取token
  getToken(): string | null {
    return localStorage.getItem('admin_token')
  },

  // 删除token
  removeToken(): void {
    localStorage.removeItem('admin_token')
  },

  // 检查是否已登录
  isAuthenticated(): boolean {
    return !!this.getToken()
  },
} 