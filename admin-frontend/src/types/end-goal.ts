// End Goal 页面配置类型定义
export interface EndGoalConfig {
  title: string;
  buttonText: string;
  infoIcon: string;
  maskImage: string;
  goalConfigs: GoalConfig[];
}

export interface GoalConfig {
  goalKey: string;
  mainColor: string;
  tag: string;
  tagIcon: string;
  description: string;
  scenario: string;
  example: string;
  quote: string;
}

export interface EndGoalConfigurableAsset {
  id: string;
  name: string;
  path: string;
  type: 'text';
  category: 'goal';
  goalKey?: string;
  goalName?: string;
  description?: string;
} 