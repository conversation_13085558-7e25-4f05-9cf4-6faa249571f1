// 配置项类型定义
export interface ConfigItem {
  category: string;
  description: string;
  items: {
    id: number;
    name: string;
    image: string;
    detailImage: string;
    script: string;
  }[];
  audios: string[];
  buttonText: string;
}

export interface PageConfig {
  id: string;
  name: string;
  navbarTitle: string;
  bottomIcon: string;
  nextIcon: string;
  enterIcon: string;
  leaveButtonText: string;
  cancelButtonText: string;
  leavingConfirmText: string;
  glichyImage: string;
  overlayImage: string;
  audioPlayingIcon: string;
  audioPausedIcon: string;
  tapTalks: ConfigItem[];
}

export interface ConfigurableAsset {
  id: string;
  name: string;
  path: string;
  type: 'image' | 'audio' | 'video' | 'json' | 'text';
  category: 'global' | 'category' | 'item';
  categoryName?: string;
  itemName?: string;
  description?: string;
}

export interface Page {
  id: string;
  name: string;
  description: string;
} 