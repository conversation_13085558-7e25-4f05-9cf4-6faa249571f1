// Onboarding 页面配置类型定义
export interface OnboardingFigure {
  id: number;
  refId: string;
  name: string;
  avatarImage: string;
  soundDemo: string;
  introduce1: string;
  isDefault: boolean;
}

export interface OnboardingSoundSetting {
  rippleGif: string;
  rippleIcon: string;
  buttonText: string;
  guideText: string;
  auditionText: string;
  auditionIcon: string;
  auditionPauseIcon: string;
  leftArrowDarkIcon: string;
  leftArrowIcon: string;
  rightArrowDarkIcon: string;
  rightArrowIcon: string;
  playingGif: string;
  playingIcon: string;
  figures: OnboardingFigure[];
}

export interface OnboardingStep {
  text1: string;
  text2: string;
  image: string;
}

export interface OnboardingConfig {
  buttonText: string;
  entryText1: string;
  entryText2: string;
  startText1: string;
  startText2: string;
  startButtonText: string;
  startImage: string;
  logoIcon: string;
  steps: OnboardingStep[];
  backgroundVideo: string;
  soundSetting: OnboardingSoundSetting;
}

export interface OnboardingConfigurableAsset {
  id: string;
  name: string;
  path: string;
  type: 'image' | 'audio' | 'video' | 'text';
  category: 'global' | 'figure';
  figureId?: number;
  figureName?: string;
  description?: string;
} 