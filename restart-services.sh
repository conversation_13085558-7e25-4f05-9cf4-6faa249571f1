#!/bin/bash

# 智能重启脚本 - 根据不同情况选择合适的重启方式
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🔧 MoodPlay 服务重启工具${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -c, --config        配置重启 (适用于 .env 文件变更)"
    echo "  -f, --full          完整重启 (重新加载镜像，适用于代码变更)"
    echo "  -s, --simple        简单重启 (仅重启容器)"
    echo "  -l, --logs          重启后显示日志"
    echo ""
    echo "示例:"
    echo "  $0 -c              # .env 文件变更后使用"
    echo "  $0 -f              # 代码变更后使用"
    echo "  $0 -s              # 简单重启服务"
    echo "  $0 -c -l           # 配置重启并显示日志"
}

# 检查 docker-compose 文件
check_files() {
    if [ ! -f "docker-compose.prod.yml" ]; then
        echo -e "${RED}❌ 错误: docker-compose.prod.yml 文件不存在${NC}"
        exit 1
    fi
    
    if [ ! -f ".env" ]; then
        echo -e "${RED}❌ 错误: .env 文件不存在${NC}"
        exit 1
    fi
}

# 简单重启 - 仅重启容器
simple_restart() {
    echo -e "${YELLOW}🔄 执行简单重启...${NC}"
    docker compose -f docker-compose.prod.yml restart
    echo -e "${GREEN}✅ 简单重启完成${NC}"
}

# 配置重启 - 重新创建容器以应用环境变量变更
config_restart() {
    echo -e "${YELLOW}🔄 执行配置重启 (重新创建容器)...${NC}"
    echo "📋 这将重新读取 .env 文件中的环境变量"
    
    # 停止并删除容器
    echo "🛑 停止并删除容器..."
    docker compose -f docker-compose.prod.yml down
    
    # 重新创建并启动容器
    echo "🚀 重新创建并启动容器..."
    docker compose -f docker-compose.prod.yml up -d
    
    echo -e "${GREEN}✅ 配置重启完成${NC}"
}

# 完整重启 - 重新加载镜像
full_restart() {
    echo -e "${YELLOW}🔄 执行完整重启 (重新加载镜像)...${NC}"
    echo "📋 这将重新加载镜像文件，适用于代码变更"
    
    # 检查镜像文件
    if [ ! -f "moodplay-backend.tar" ] || [ ! -f "moodplay-admin-frontend.tar" ]; then
        echo -e "${RED}❌ 错误: 镜像文件不存在，请先运行构建脚本生成镜像${NC}"
        exit 1
    fi
    
    # 调用完整的加载和启动脚本
    ./load-and-start.sh
    
    echo -e "${GREEN}✅ 完整重启完成${NC}"
}

# 显示服务状态
show_status() {
    echo ""
    echo -e "${BLUE}📊 当前服务状态:${NC}"
    docker compose -f docker-compose.prod.yml ps
}

# 显示日志
show_logs() {
    echo ""
    echo -e "${BLUE}📋 服务日志 (按 Ctrl+C 退出):${NC}"
    docker compose -f docker-compose.prod.yml logs -f
}

# 主逻辑
main() {
    local config_restart_flag=false
    local full_restart_flag=false
    local simple_restart_flag=false
    local show_logs_flag=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--config)
                config_restart_flag=true
                shift
                ;;
            -f|--full)
                full_restart_flag=true
                shift
                ;;
            -s|--simple)
                simple_restart_flag=true
                shift
                ;;
            -l|--logs)
                show_logs_flag=true
                shift
                ;;
            *)
                echo -e "${RED}❌ 未知选项: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定任何重启选项，显示帮助
    if [ "$config_restart_flag" = false ] && [ "$full_restart_flag" = false ] && [ "$simple_restart_flag" = false ]; then
        echo -e "${YELLOW}⚠️  请指定重启类型${NC}"
        echo ""
        show_help
        exit 1
    fi
    
    # 检查必要文件
    check_files
    
    # 执行相应的重启操作
    if [ "$full_restart_flag" = true ]; then
        full_restart
    elif [ "$config_restart_flag" = true ]; then
        config_restart
    elif [ "$simple_restart_flag" = true ]; then
        simple_restart
    fi
    
    # 显示状态
    show_status
    
    # 如果需要显示日志
    if [ "$show_logs_flag" = true ]; then
        show_logs
    fi
}

# 运行主函数
main "$@"
