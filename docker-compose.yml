services:
  backend:
    container_name: backend
    build:
      context: ./backend
      target: development
    volumes:
      - ./backend:/usr/src/app
      - /usr/src/app/node_modules
      - ./backend/logs:/usr/src/app/logs
    command: yarn dev
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
    depends_on:
      - mysql
      - redis
    env_file:
      - .env
    restart: unless-stopped
    networks:
      - app-network

  admin-frontend:
    container_name: admin-frontend
    build:
      context: ./admin-frontend
      target: development
    volumes:
      - ./admin-frontend:/usr/src/app
      - /usr/src/app/node_modules
    ports:
      - "5173:5173"
    environment:
      - CHOKIDAR_USEPOLLING=true
    networks:
      - app-network

  nginx:
    container_name: nginx
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - admin-frontend
    networks:
      - app-network

  mysql:
    container_name: mysql
    image: mysql:8.0
    env_file:
      - .env
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
      - MYSQL_DATABASE=${DB_NAME}
      - MYSQL_USER=${DB_USER}
      - MYSQL_PASSWORD=${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci --default-time-zone='+08:00'
    restart: unless-stopped
    networks:
      - app-network

  redis:
    container_name: redis
    image: redis:alpine
    volumes:
      - redis_data:/data
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
    name: my-app-network

volumes:
  mysql_data:
  redis_data: 