# 生产环境nginx配置
server {
    listen 80;
    server_name moodplay.top;
    
    # 添加 certbot 验证目录
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    # 重定向HTTP到HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name moodplay.top;

    ssl_certificate /etc/letsencrypt/live/moodplay.top/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/moodplay.top/privkey.pem;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    
    # 管理后台路由 - 优先级最高，匹配所有 /admin/ 开头的请求
    location /admin/ {
        proxy_pass http://admin-frontend-prod:5173/admin/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 反向代理到生产环境Node应用
    location / {
        proxy_pass http://backend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_read_timeout 120s; # 后端响应读取超时2分钟
        proxy_connect_timeout 60s; # 连接后端超时1分钟
        proxy_send_timeout 60s; # 发送请求到后端超时1分钟
    }
} 