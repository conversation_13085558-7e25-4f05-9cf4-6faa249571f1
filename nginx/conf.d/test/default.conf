# 测试环境nginx配置
server {
    listen 80;
    server_name test.moodplay.top localhost 127.0.0.1;
    
    # 管理后台路由 - 优先级最高，匹配所有 /admin/ 开头的请求
    location /admin/ {
        proxy_pass http://admin-frontend-test:5173/admin/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件服务 - 仅处理非 /admin/ 路径的静态文件
    location ~* ^/(?!admin/).*\.(html|htm|css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|txt)$ {
        root /var/www/html;
        try_files $uri $uri/ =404;
        expires 1h;
        add_header Cache-Control "public, immutable";
    }
    
    # API请求转发到后端
    location /api {
        proxy_pass http://backend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_read_timeout 120s; # 后端响应读取超时2分钟
        proxy_connect_timeout 60s; # 连接后端超时1分钟
        proxy_send_timeout 60s; # 发送请求到后端超时1分钟
    }
    
    # 健康检查端点 - 直接转发到后端
    location /health {
        proxy_pass http://backend:3000/api/health-check;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 根路径处理 - 可以返回默认页面或转发到前端应用
    location = / {
        root /var/www/html;
        try_files /index.html =404;
    }
    
    # 其他路径的fallback处理
    location / {
        proxy_pass http://backend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_read_timeout 120s;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
    }
}

# 测试环境的HTTPS配置
server {
    listen 443 ssl;
    server_name test.moodplay.top;

    # 使用Let's Encrypt证书
    ssl_certificate /etc/letsencrypt/live/test.moodplay.top/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/test.moodplay.top/privkey.pem;
    
    # 基本的SSL配置，测试环境可以适当放宽
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    
    # 管理后台路由 - 优先级最高，匹配所有 /admin/ 开头的请求
    location /admin/ {
        proxy_pass http://admin-frontend-test:5173/admin/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件服务 - 仅处理非 /admin/ 路径的静态文件
    location ~* ^/(?!admin/).*\.(html|htm|css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|txt)$ {
        root /var/www/html;
        try_files $uri $uri/ =404;
        expires 1h;
        add_header Cache-Control "public, immutable";
    }
    
    # API请求转发到后端
    location /api {
        proxy_pass http://backend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_read_timeout 120s;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
    }
    
    # 健康检查端点
    location /health {
        proxy_pass http://backend:3000/api/health-check;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 根路径处理
    location = / {
        root /var/www/html;
        try_files /index.html =404;
    }
    
    # 其他路径的fallback处理
    location / {
        proxy_pass http://backend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_read_timeout 120s;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
    }
} 