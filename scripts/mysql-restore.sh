#!/bin/bash

# MySQL 数据库恢复脚本
# 用于从备份文件恢复数据库

# 配置变量
BACKUP_DIR="/backup"
MYSQL_CONTAINER="mysql"
DB_NAME="${DB_NAME:-moodplay}"
DB_USER="${DB_USER:-admin}"
DB_PASSWORD="${DB_PASSWORD}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 <backup_file>"
    echo ""
    echo "参数:"
    echo "  backup_file    备份文件名（不包含路径，例如: moodplay_backup_20240131_120000.sql.gz）"
    echo ""
    echo "示例:"
    echo "  $0 moodplay_backup_20240131_120000.sql.gz"
    echo ""
    echo "可用的备份文件:"
    list_available_backups
}

# 列出可用的备份文件
list_available_backups() {
    docker exec "$MYSQL_CONTAINER" ls -1 "$BACKUP_DIR"/moodplay_backup_*.sql.gz 2>/dev/null | sed "s|$BACKUP_DIR/||" || echo "  没有找到备份文件"
}

# 检查必要的环境变量
check_env() {
    if [ -z "$DB_PASSWORD" ]; then
        log_error "DB_PASSWORD 环境变量未设置"
        exit 1
    fi
}

# 检查Docker容器是否运行
check_container() {
    if ! docker ps | grep -q "$MYSQL_CONTAINER"; then
        log_error "MySQL容器 '$MYSQL_CONTAINER' 未运行"
        exit 1
    fi
}

# 检查备份文件是否存在
check_backup_file() {
    local backup_file="$1"
    local full_path="$BACKUP_DIR/$backup_file"
    
    if ! docker exec "$MYSQL_CONTAINER" test -f "$full_path"; then
        log_error "备份文件不存在: $full_path"
        log_info "可用的备份文件:"
        list_available_backups
        exit 1
    fi
    
    log_info "找到备份文件: $full_path"
}

# 确认恢复操作
confirm_restore() {
    local backup_file="$1"
    
    log_warn "警告: 此操作将完全替换数据库 '$DB_NAME' 的所有数据！"
    log_warn "备份文件: $backup_file"
    log_warn "目标数据库: $DB_NAME"
    echo ""
    
    read -p "确定要继续吗？请输入 'YES' 确认: " confirmation
    
    if [ "$confirmation" != "YES" ]; then
        log_info "操作已取消"
        exit 0
    fi
}

# 创建当前数据库的备份
create_pre_restore_backup() {
    log_info "创建恢复前的安全备份..."
    
    local pre_backup_file="pre_restore_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    docker exec "$MYSQL_CONTAINER" mysqldump \
        -u"$DB_USER" \
        -p"$DB_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --default-character-set=utf8mb4 \
        "$DB_NAME" > "/tmp/$pre_backup_file"
    
    if [ $? -eq 0 ]; then
        docker cp "/tmp/$pre_backup_file" "$MYSQL_CONTAINER:$BACKUP_DIR/"
        rm "/tmp/$pre_backup_file"
        docker exec "$MYSQL_CONTAINER" gzip "$BACKUP_DIR/$pre_backup_file"
        log_info "安全备份已创建: $pre_backup_file.gz"
    else
        log_error "创建安全备份失败"
        exit 1
    fi
}

# 恢复数据库
restore_database() {
    local backup_file="$1"
    local full_path="$BACKUP_DIR/$backup_file"
    
    log_info "开始恢复数据库..."
    
    # 解压备份文件到临时位置
    docker exec "$MYSQL_CONTAINER" gunzip -c "$full_path" > "/tmp/restore_temp.sql"
    
    if [ $? -ne 0 ]; then
        log_error "解压备份文件失败"
        exit 1
    fi
    
    # 恢复数据库
    docker exec -i "$MYSQL_CONTAINER" mysql \
        -u"$DB_USER" \
        -p"$DB_PASSWORD" \
        "$DB_NAME" < "/tmp/restore_temp.sql"
    
    if [ $? -eq 0 ]; then
        log_info "数据库恢复成功"
        # 清理临时文件
        docker exec "$MYSQL_CONTAINER" rm -f "/tmp/restore_temp.sql"
    else
        log_error "数据库恢复失败"
        docker exec "$MYSQL_CONTAINER" rm -f "/tmp/restore_temp.sql"
        exit 1
    fi
}

# 验证恢复结果
verify_restore() {
    log_info "验证恢复结果..."
    
    # 检查数据库连接
    docker exec "$MYSQL_CONTAINER" mysql \
        -u"$DB_USER" \
        -p"$DB_PASSWORD" \
        -e "SELECT 1;" "$DB_NAME" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log_info "数据库连接正常"
        
        # 显示表数量
        local table_count=$(docker exec "$MYSQL_CONTAINER" mysql \
            -u"$DB_USER" \
            -p"$DB_PASSWORD" \
            -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$DB_NAME';" \
            -s -N "$DB_NAME")
        
        log_info "数据库中有 $table_count 个表"
    else
        log_error "数据库连接失败"
        exit 1
    fi
}

# 主函数
main() {
    local backup_file="$1"
    
    if [ -z "$backup_file" ]; then
        show_usage
        exit 1
    fi
    
    log_info "=== MySQL 数据库恢复开始 ==="
    log_info "时间: $(date)"
    log_info "备份文件: $backup_file"
    log_info "目标数据库: $DB_NAME"
    
    check_env
    check_container
    check_backup_file "$backup_file"
    confirm_restore "$backup_file"
    create_pre_restore_backup
    restore_database "$backup_file"
    verify_restore
    
    log_info "=== 恢复完成 ==="
}

# 如果脚本被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
