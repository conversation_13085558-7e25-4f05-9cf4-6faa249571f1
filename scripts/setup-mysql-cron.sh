#!/bin/bash

# 设置MySQL定时备份的脚本
# 在生产服务器上运行此脚本来配置自动备份

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_SCRIPT="$SCRIPT_DIR/mysql-backup.sh"
LOG_FILE="/var/log/mysql-backup.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root权限运行此脚本"
        exit 1
    fi
}

# 检查备份脚本是否存在
check_backup_script() {
    if [ ! -f "$BACKUP_SCRIPT" ]; then
        log_error "备份脚本不存在: $BACKUP_SCRIPT"
        exit 1
    fi
    
    # 确保脚本可执行
    chmod +x "$BACKUP_SCRIPT"
    log_info "备份脚本已设置为可执行: $BACKUP_SCRIPT"
}

# 创建日志目录
create_log_dir() {
    local log_dir=$(dirname "$LOG_FILE")
    mkdir -p "$log_dir"
    touch "$LOG_FILE"
    log_info "日志文件: $LOG_FILE"
}

# 创建cron任务
setup_cron() {
    log_info "设置定时备份任务..."
    
    # 创建临时cron文件
    local temp_cron="/tmp/mysql_backup_cron"
    
    # 获取当前用户的cron任务
    crontab -l > "$temp_cron" 2>/dev/null || true
    
    # 检查是否已存在备份任务
    if grep -q "mysql-backup.sh" "$temp_cron"; then
        log_warn "检测到已存在的备份任务，将替换..."
        # 移除旧的备份任务
        grep -v "mysql-backup.sh" "$temp_cron" > "${temp_cron}.new"
        mv "${temp_cron}.new" "$temp_cron"
    fi
    
    # 添加新的备份任务
    cat >> "$temp_cron" << EOF

# MySQL 自动备份任务 - 每天凌晨2点执行
0 2 * * * cd $PROJECT_DIR && source .env.production && $BACKUP_SCRIPT >> $LOG_FILE 2>&1

# MySQL 备份日志轮转 - 每周日凌晨3点清理30天前的日志
0 3 * * 0 find $(dirname $LOG_FILE) -name "mysql-backup.log*" -mtime +30 -delete

EOF
    
    # 安装新的cron任务
    crontab "$temp_cron"
    
    # 清理临时文件
    rm -f "$temp_cron"
    
    log_info "定时备份任务已设置:"
    log_info "  - 每天凌晨2点自动备份"
    log_info "  - 每周日凌晨3点清理旧日志"
}

# 显示当前cron任务
show_cron() {
    log_info "当前的cron任务:"
    crontab -l | grep -A 5 -B 5 "mysql-backup" || log_warn "没有找到MySQL备份相关的cron任务"
}

# 测试备份脚本
test_backup() {
    log_info "测试备份脚本..."
    
    cd "$PROJECT_DIR"
    
    # 加载环境变量
    if [ -f ".env.production" ]; then
        source .env.production
        log_info "已加载生产环境变量"
    else
        log_error "未找到 .env.production 文件"
        exit 1
    fi
    
    # 执行测试备份
    log_info "执行测试备份（这可能需要几分钟）..."
    if "$BACKUP_SCRIPT"; then
        log_info "测试备份成功！"
    else
        log_error "测试备份失败"
        exit 1
    fi
}

# 显示使用说明
show_usage() {
    echo "MySQL 定时备份设置脚本"
    echo ""
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --setup     设置定时备份任务"
    echo "  --test      测试备份脚本"
    echo "  --show      显示当前cron任务"
    echo "  --help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  sudo $0 --setup    # 设置定时备份"
    echo "  sudo $0 --test     # 测试备份功能"
    echo "  $0 --show          # 查看当前任务"
}

# 主函数
main() {
    case "$1" in
        --setup)
            check_root
            check_backup_script
            create_log_dir
            setup_cron
            show_cron
            log_info "定时备份设置完成！"
            log_info "建议运行 '$0 --test' 来测试备份功能"
            ;;
        --test)
            check_backup_script
            test_backup
            ;;
        --show)
            show_cron
            ;;
        --help|"")
            show_usage
            ;;
        *)
            log_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
}

# 如果脚本被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
