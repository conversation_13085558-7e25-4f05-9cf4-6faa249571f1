#!/bin/bash

# 设置默认变量
ENV="production"
COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.production"
BRANCH="main"  # 默认分支

# 解析命令行参数
while [[ "$#" -gt 0 ]]; do
    case $1 in
        -b|--branch) BRANCH="$2"; shift ;;
        *) echo "未知参数: $1"; exit 1 ;;
    esac
    shift
done

echo "准备部署分支: $BRANCH"

# 获取最新远程信息
git fetch --all

# 检查分支是否存在
if ! git show-ref --verify --quiet refs/remotes/origin/$BRANCH; then
    echo "错误: 远程分支 origin/$BRANCH 不存在!"
    exit 1
fi

# 切换到指定分支
git checkout $BRANCH

# 拉取最新代码 (使用--rebase避免合并提交)
git pull --rebase origin $BRANCH

# 构建并启动容器
docker compose --env-file $ENV_FILE -f $COMPOSE_FILE down
docker compose --env-file $ENV_FILE -f $COMPOSE_FILE build
docker compose --env-file $ENV_FILE -f $COMPOSE_FILE up -d

# 显示容器状态
docker compose -f $COMPOSE_FILE ps

echo "生产环境部署完成！使用分支: $BRANCH"
