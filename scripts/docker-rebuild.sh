#!/bin/bash

# 定义变量
USE_NO_CACHE=false

# 解析参数
while [[ $# -gt 0 ]]; do
    case "$1" in
        --no-cache)
            USE_NO_CACHE=true
            shift
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

echo "正在停止并移除容器..."
docker-compose down

echo -e "\n正在重新构建镜像..."
if [ "$USE_NO_CACHE" = true ]; then
    echo "使用 --no-cache 选项"
    docker-compose build --no-cache
else
    docker-compose build
fi

echo -e "\n启动服务..."
docker-compose up -d

echo -e "\n操作完成！"
