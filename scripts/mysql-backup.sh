#!/bin/bash

# MySQL 数据库备份脚本
# 适用于生产环境的基本备份策略

# 配置变量
BACKUP_DIR="/backup"
MYSQL_CONTAINER="mysql"
DB_NAME="${DB_NAME:-moodplay}"
DB_USER="${DB_USER:-admin}"
DB_PASSWORD="${DB_PASSWORD}"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="moodplay_backup_${DATE}.sql"
RETENTION_DAYS=7

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的环境变量
check_env() {
    if [ -z "$DB_PASSWORD" ]; then
        log_error "DB_PASSWORD 环境变量未设置"
        exit 1
    fi
}

# 检查Docker容器是否运行
check_container() {
    if ! docker ps | grep -q "$MYSQL_CONTAINER"; then
        log_error "MySQL容器 '$MYSQL_CONTAINER' 未运行"
        exit 1
    fi
}

# 创建备份目录
create_backup_dir() {
    docker exec "$MYSQL_CONTAINER" mkdir -p "$BACKUP_DIR" 2>/dev/null || true
}

# 执行数据库备份
backup_database() {
    log_info "开始备份数据库 '$DB_NAME'..."
    
    # 使用mysqldump进行备份
    docker exec "$MYSQL_CONTAINER" mysqldump \
        -u"$DB_USER" \
        -p"$DB_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --default-character-set=utf8mb4 \
        "$DB_NAME" > "/tmp/$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        # 将备份文件复制到容器内的备份目录
        docker cp "/tmp/$BACKUP_FILE" "$MYSQL_CONTAINER:$BACKUP_DIR/"
        rm "/tmp/$BACKUP_FILE"
        
        # 压缩备份文件
        docker exec "$MYSQL_CONTAINER" gzip "$BACKUP_DIR/$BACKUP_FILE"
        
        log_info "备份完成: $BACKUP_FILE.gz"
        
        # 显示备份文件大小
        BACKUP_SIZE=$(docker exec "$MYSQL_CONTAINER" ls -lh "$BACKUP_DIR/$BACKUP_FILE.gz" | awk '{print $5}')
        log_info "备份文件大小: $BACKUP_SIZE"
    else
        log_error "备份失败"
        exit 1
    fi
}

# 清理旧备份文件
cleanup_old_backups() {
    log_info "清理 $RETENTION_DAYS 天前的备份文件..."
    
    docker exec "$MYSQL_CONTAINER" find "$BACKUP_DIR" \
        -name "moodplay_backup_*.sql.gz" \
        -type f \
        -mtime +$RETENTION_DAYS \
        -delete
    
    log_info "清理完成"
}

# 列出现有备份
list_backups() {
    log_info "现有备份文件:"
    docker exec "$MYSQL_CONTAINER" ls -lh "$BACKUP_DIR"/moodplay_backup_*.sql.gz 2>/dev/null || log_warn "没有找到备份文件"
}

# 验证备份文件
verify_backup() {
    local backup_file="$BACKUP_DIR/$BACKUP_FILE.gz"
    
    log_info "验证备份文件..."
    
    # 检查文件是否存在且不为空
    if docker exec "$MYSQL_CONTAINER" test -s "$backup_file"; then
        log_info "备份文件验证通过"
    else
        log_error "备份文件验证失败"
        exit 1
    fi
}

# 主函数
main() {
    log_info "=== MySQL 数据库备份开始 ==="
    log_info "时间: $(date)"
    log_info "数据库: $DB_NAME"
    
    check_env
    check_container
    create_backup_dir
    backup_database
    verify_backup
    cleanup_old_backups
    list_backups
    
    log_info "=== 备份完成 ==="
}

# 如果脚本被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
