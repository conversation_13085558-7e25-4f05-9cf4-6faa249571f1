import requests
import random
import time
import threading
from concurrent.futures import ThreadPoolExecutor
import schedule
import signal

BASE_URL = "https://test.moodplay.top"
AUTH_HEADER = {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsIm9wZW5pZCI6Im9PTDVpN005UEhncHM2Q3lCdnZzTXg5dkV5MDgiLCJpYXQiOjE3NTI3NjA4OTksImV4cCI6MTc1MzM2NTY5OX0.1lqCZUjKmEMq3tYQ_b2kRqpPsxPX75e9UgePJtgW60M",
    "Content-Type": "application/json; charset=utf-8"
}
GOAL_IMAGE_KEY = "assets/pages/end-goal/松弛时刻/images/101.jpg"

with open('text_list.txt', 'r', encoding='utf-8') as f:
    text_list = [line.strip() for line in f if line.strip()]

text_index = 0
text_lock = threading.Lock()
executor = ThreadPoolExecutor(max_workers=20)

stats_lock = threading.Lock()
stats = {
    'total_sessions': 0,
    'failures': 0,
    'step5_times': []
}

stop_signal = False

def get_next_text():
    global text_index
    with text_lock:
        text = text_list[text_index % len(text_list)]
        text_index += 1
    return text


def simulate_user_session():
    global stats
    try:
        text = get_next_text()
        res = requests.post(f"{BASE_URL}/api/play-session", headers=AUTH_HEADER, json={"text": text}).json()
        if res['code'] != 0:
            raise Exception("创建 session 失败")
        playSessionId = res['data']['playSessionId']

        res = requests.get(f"{BASE_URL}/api/play-session/{playSessionId}", headers=AUTH_HEADER).json()
        emotions = res['data']['detectedEmotions']
        goals = res['data']['suggestedGoals']

        selected_emotions = random.sample(emotions, random.randint(1, len(emotions)))
        selectedEmotionIds = [e['id'] for e in selected_emotions]
        requests.put(f"{BASE_URL}/api/emotion/{playSessionId}", headers=AUTH_HEADER, json={"selectedEmotionIds": selectedEmotionIds})

        selected_goal = random.choice(goals)
        goal_body = {"selectedGoalIds": [selected_goal['id']], "goalImageKey": GOAL_IMAGE_KEY}
        requests.put(f"{BASE_URL}/api/goal/{playSessionId}", headers=AUTH_HEADER, json=goal_body)

        print(f"Session {playSessionId} 正在生成开头音频...")
        step5_start = time.time()
        requests.post(f"{BASE_URL}/api/play-session/{playSessionId}/generate-empathy", headers=AUTH_HEADER, json={})
        step5_duration = time.time() - step5_start
        print(f"Session {playSessionId} 开头音频生成耗时: {step5_duration:.2f}秒")

        with stats_lock:
            stats['total_sessions'] += 1
            stats['step5_times'].append(step5_duration)

        pre_audio = None
        while not pre_audio:
            res = requests.get(f"{BASE_URL}/api/play-session/{playSessionId}/get-play-info", headers=AUTH_HEADER).json()
            if res['code'] == 0 and res['data'].get('preAudioUrl'):
                pre_audio = res['data']['preAudioUrl']
            else:
                time.sleep(2)

        time.sleep(60)
        workflow_audio = None
        while not workflow_audio:
            res = requests.get(f"{BASE_URL}/api/play-session/{playSessionId}/workflow-status", headers=AUTH_HEADER).json()
            if res['code'] == 0 and res['data'].get('workflowAudioUrl'):
                workflow_audio = res['data']['workflowAudioUrl']
            else:
                time.sleep(2)

        time.sleep(480)
        requests.put(f"{BASE_URL}/play-session/{playSessionId}/finish", headers=AUTH_HEADER, json={})

    except Exception as e:
        with stats_lock:
            stats['failures'] += 1
        print(f"Session 错误: {e}")


def schedule_task():
    if not stop_signal:
        executor.submit(simulate_user_session)


def run_fixed_interval_test(interval_seconds=10, duration_minutes=10):
    global stop_signal
    print(f"开始压测: 每 {interval_seconds} 秒触发一次，持续 {duration_minutes} 分钟")

    schedule.every(interval_seconds).seconds.do(schedule_task)
    start_time = time.time()

    try:
        while time.time() - start_time < duration_minutes * 60 and not stop_signal:
            schedule.run_pending()
            time.sleep(0.1)

    except KeyboardInterrupt:
        print("\n收到中断信号，正在安全停止...")
        stop_signal = True

    executor.shutdown(wait=True)
    print("压测结束")
    report_stats()


def report_stats():
    with stats_lock:
        total = stats['total_sessions']
        fail = stats['failures']
        times = stats['step5_times']

    if times:
        avg_time = sum(times) / len(times)
        max_time = max(times)
        min_time = min(times)
    else:
        avg_time = max_time = min_time = 0

    print("\n========= 压测统计 =========")
    print(f"总Session数量：{total}")
    print(f"失败数量：{fail}")
    print(f"Step5 耗时 - 平均: {avg_time:.2f}s, 最大: {max_time:.2f}s, 最小: {min_time:.2f}s")
    print("================================")


def signal_handler(sig, frame):
    global stop_signal
    print("\n强制退出信号已捕获，停止新任务调度...")
    stop_signal = True

signal.signal(signal.SIGINT, signal_handler)


if __name__ == "__main__":
    run_fixed_interval_test(interval_seconds=10, duration_minutes=10)
