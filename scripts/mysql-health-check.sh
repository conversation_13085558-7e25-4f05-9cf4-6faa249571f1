#!/bin/bash

# MySQL 健康检查脚本
# 用于监控MySQL数据库的运行状态和性能

# 配置变量
MYSQL_CONTAINER="mysql"
DB_NAME="${DB_NAME:-moodplay}"
DB_USER="${DB_USER:-admin}"
DB_PASSWORD="${DB_PASSWORD}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# 自动加载环境变量
load_env() {
    # 查找 .env 文件的可能位置
    local env_files=(
        ".env"
        "../.env"
        "../../.env"
        ".env.production"
        "../.env.production"
        "../../.env.production"
    )

    for env_file in "${env_files[@]}"; do
        if [ -f "$env_file" ]; then
            log_info "加载环境变量文件: $env_file"
            source "$env_file"
            break
        fi
    done
}

# 检查必要的环境变量
check_env() {
    # 如果环境变量未设置，尝试加载
    if [ -z "$DB_PASSWORD" ]; then
        load_env
    fi

    if [ -z "$DB_PASSWORD" ]; then
        log_error "DB_PASSWORD 环境变量未设置"
        log_error "请确保 .env 文件存在并包含 DB_PASSWORD 变量"
        log_error "或者手动设置环境变量: export DB_PASSWORD=your_password"
        exit 1
    fi
}

# 检查Docker容器状态
check_container_status() {
    log_header "容器状态检查"
    
    if docker ps | grep -q "$MYSQL_CONTAINER"; then
        log_info "MySQL容器正在运行"
        
        # 显示容器详细信息
        local container_info=$(docker ps --filter "name=$MYSQL_CONTAINER" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}")
        echo "$container_info"
    else
        log_error "MySQL容器未运行"
        return 1
    fi
}

# 检查数据库连接
check_database_connection() {
    log_header "数据库连接检查"
    
    if docker exec "$MYSQL_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1; then
        log_info "数据库连接正常"
        
        # 显示MySQL版本
        local mysql_version=$(docker exec "$MYSQL_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT VERSION();" -s -N 2>/dev/null)
        log_info "MySQL版本: $mysql_version"
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 检查数据库大小
check_database_size() {
    log_header "数据库大小检查"
    
    local db_size=$(docker exec "$MYSQL_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASSWORD" -e "
        SELECT 
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'DB Size (MB)'
        FROM information_schema.tables 
        WHERE table_schema='$DB_NAME';
    " -s -N 2>/dev/null)
    
    if [ -n "$db_size" ]; then
        log_info "数据库大小: ${db_size} MB"
    else
        log_warn "无法获取数据库大小"
    fi
}

# 检查表状态
check_table_status() {
    log_header "表状态检查"
    
    local table_count=$(docker exec "$MYSQL_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASSWORD" -e "
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema='$DB_NAME';
    " -s -N 2>/dev/null)
    
    if [ -n "$table_count" ]; then
        log_info "数据库中有 $table_count 个表"
        
        # 显示表的详细信息
        echo "表详细信息:"
        docker exec "$MYSQL_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASSWORD" -e "
            SELECT 
                table_name AS '表名',
                table_rows AS '行数',
                ROUND((data_length + index_length) / 1024 / 1024, 2) AS '大小(MB)'
            FROM information_schema.tables 
            WHERE table_schema='$DB_NAME'
            ORDER BY (data_length + index_length) DESC;
        " 2>/dev/null
    else
        log_warn "无法获取表信息"
    fi
}

# 检查MySQL进程状态
check_mysql_processes() {
    log_header "MySQL进程状态"
    
    local process_count=$(docker exec "$MYSQL_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASSWORD" -e "SHOW PROCESSLIST;" -s -N 2>/dev/null | wc -l)
    
    if [ -n "$process_count" ]; then
        log_info "当前活跃连接数: $process_count"
        
        # 显示长时间运行的查询
        echo "长时间运行的查询 (>10秒):"
        docker exec "$MYSQL_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASSWORD" -e "
            SELECT 
                ID,
                USER,
                HOST,
                DB,
                COMMAND,
                TIME,
                STATE,
                LEFT(INFO, 50) AS QUERY
            FROM information_schema.PROCESSLIST 
            WHERE TIME > 10 AND COMMAND != 'Sleep'
            ORDER BY TIME DESC;
        " 2>/dev/null
    else
        log_warn "无法获取进程信息"
    fi
}

# 检查MySQL状态变量
check_mysql_status() {
    log_header "MySQL状态变量"
    
    echo "关键状态变量:"
    docker exec "$MYSQL_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASSWORD" -e "
        SELECT 
            VARIABLE_NAME AS '变量名',
            VARIABLE_VALUE AS '值'
        FROM performance_schema.global_status 
        WHERE VARIABLE_NAME IN (
            'Uptime',
            'Connections',
            'Max_used_connections',
            'Threads_connected',
            'Threads_running',
            'Slow_queries',
            'Questions',
            'Innodb_buffer_pool_read_requests',
            'Innodb_buffer_pool_reads'
        );
    " 2>/dev/null
}

# 检查磁盘空间
check_disk_space() {
    log_header "磁盘空间检查"
    
    echo "MySQL数据目录磁盘使用情况:"
    docker exec "$MYSQL_CONTAINER" df -h /var/lib/mysql 2>/dev/null || log_warn "无法获取磁盘空间信息"
    
    echo ""
    echo "备份目录磁盘使用情况:"
    docker exec "$MYSQL_CONTAINER" df -h /backup 2>/dev/null || log_warn "备份目录不存在或无法访问"
}

# 检查最近的备份
check_recent_backups() {
    log_header "备份文件检查"
    
    echo "最近的备份文件:"
    docker exec "$MYSQL_CONTAINER" ls -lht /backup/moodplay_backup_*.sql.gz 2>/dev/null | head -5 || log_warn "没有找到备份文件"
}

# 检查错误日志
check_error_logs() {
    log_header "错误日志检查"
    
    echo "最近的错误日志 (最后20行):"
    docker exec "$MYSQL_CONTAINER" tail -20 /var/log/mysql/error.log 2>/dev/null || log_warn "无法访问错误日志"
}

# 生成健康报告摘要
generate_summary() {
    log_header "健康检查摘要"
    
    local issues=0
    
    # 检查容器状态
    if ! docker ps | grep -q "$MYSQL_CONTAINER"; then
        log_error "容器未运行"
        ((issues++))
    fi
    
    # 检查数据库连接
    if ! docker exec "$MYSQL_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1; then
        log_error "数据库连接失败"
        ((issues++))
    fi
    
    # 检查磁盘空间 (警告阈值: 80%)
    local disk_usage=$(docker exec "$MYSQL_CONTAINER" df /var/lib/mysql 2>/dev/null | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ -n "$disk_usage" ] && [ "$disk_usage" -gt 80 ]; then
        log_warn "磁盘使用率过高: ${disk_usage}%"
        ((issues++))
    fi
    
    if [ $issues -eq 0 ]; then
        log_info "所有检查项目正常 ✓"
    else
        log_warn "发现 $issues 个问题需要关注"
    fi
}

# 主函数
main() {
    echo "MySQL 健康检查报告"
    echo "时间: $(date)"
    echo "数据库: $DB_NAME"
    echo ""
    
    check_env
    check_container_status
    check_database_connection
    check_database_size
    check_table_status
    check_mysql_processes
    check_mysql_status
    check_disk_space
    check_recent_backups
    check_error_logs
    generate_summary
    
    echo ""
    echo "健康检查完成"
}

# 如果脚本被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
