# Redis Configuration for Test Environment
# Optimized for testing scenarios with focus on performance and memory efficiency
# Data persistence is disabled as it's not needed in test environment

# ========================================================================================
# NETWORK AND BASIC SETTINGS
# ========================================================================================

# Accept connections from any IP (safe in containerized test environment)
bind 0.0.0.0

# Default port
port 6379

# Disable protected mode since we're in a controlled test environment
protected-mode no

# TCP listen backlog
tcp-backlog 511

# Close connection after client is idle for N seconds (0 to disable)
timeout 300

# TCP keepalive
tcp-keepalive 300

# ========================================================================================
# MEMORY MANAGEMENT
# ========================================================================================

# Set maximum memory limit for test environment (256MB)
# This prevents Redis from consuming too much memory during tests
maxmemory 1024mb

# Memory eviction policy - use allkeys-lru for test environment
# This evicts least recently used keys when memory limit is reached
maxmemory-policy allkeys-lru

# Number of samples to check for LRU eviction
maxmemory-samples 5

# ========================================================================================
# PERSISTENCE SETTINGS - DISABLED FOR TEST ENVIRONMENT
# ========================================================================================

# Disable RDB snapshots completely
save ""

# Disable RDB file creation
rdbcompression no
rdbchecksum no

# Disable AOF (Append Only File) logging
appendonly no

# ========================================================================================
# LOGGING
# ========================================================================================

# Log level for test environment (notice is good balance)
loglevel notice

# Log to stdout (will be captured by Docker)
logfile ""

# ========================================================================================
# CLIENT SETTINGS
# ========================================================================================

# Maximum number of connected clients
maxclients 1000

# ========================================================================================
# PERFORMANCE OPTIMIZATIONS FOR TESTING
# ========================================================================================

# Disable slow log for test environment
slowlog-log-slower-than -1

# Hash table rehashing
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List compression
list-max-ziplist-size -2
list-compress-depth 0

# Set compression
set-max-intset-entries 512

# Sorted set compression
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog sparse representation
hll-sparse-max-bytes 3000

# ========================================================================================
# SECURITY SETTINGS
# ========================================================================================

# Disable dangerous commands in test environment
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG ""
rename-command SHUTDOWN ""
rename-command DEBUG ""

# ========================================================================================
# ADVANCED SETTINGS
# ========================================================================================

# Disable automatic memory defragmentation to avoid unpredictable behavior during tests
activedefrag no

# Disable lazy freeing for predictable test behavior
lazyfree-lazy-eviction no
lazyfree-lazy-expire no
lazyfree-lazy-server-del no

# Client output buffer limits (reduced for test environment)
client-output-buffer-limit normal 32mb 16mb 60
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Frequency of background tasks
hz 10

# Dynamic HZ is enabled by default in modern Redis versions

# ========================================================================================
# TEST-SPECIFIC OPTIMIZATIONS
# ========================================================================================

# Disable replication and clustering features
replica-read-only yes
replica-serve-stale-data yes

# Optimize for single-threaded workload typical in tests
io-threads 1
io-threads-do-reads no

# ========================================================================================
# MODULES AND EXTENSIONS
# ========================================================================================

# No additional modules needed for test environment

# ========================================================================================
# MEMORY USAGE REPORTING
# ========================================================================================

# Memory usage tracking is available via INFO memory command
