#!/bin/bash

# Redis Test Environment Utilities
# This script provides helpful commands for managing Redis in the test environment

set -e

REDIS_CONTAINER="redis"
COMPOSE_FILE="docker-compose.test.yml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check if Redis container is running
check_redis_status() {
    if docker ps | grep -q "$REDIS_CONTAINER"; then
        print_status "Redis container is running"
        return 0
    else
        print_error "Redis container is not running"
        return 1
    fi
}

# Start Redis with test configuration
start_redis() {
    print_header "Starting Redis Test Environment"
    docker-compose -f "$COMPOSE_FILE" up -d redis
    sleep 2
    if check_redis_status; then
        print_status "Redis started successfully"
    else
        print_error "Failed to start Redis"
        exit 1
    fi
}

# Stop Redis
stop_redis() {
    print_header "Stopping Redis Test Environment"
    docker-compose -f "$COMPOSE_FILE" stop redis
    print_status "Redis stopped"
}

# Restart Redis
restart_redis() {
    print_header "Restarting Redis Test Environment"
    stop_redis
    start_redis
}

# Connect to Redis CLI
connect_redis() {
    if ! check_redis_status; then
        print_error "Redis is not running. Start it first with: $0 start"
        exit 1
    fi
    print_status "Connecting to Redis CLI..."
    docker exec -it "$REDIS_CONTAINER" redis-cli
}

# Show Redis memory information
show_memory_info() {
    if ! check_redis_status; then
        print_error "Redis is not running"
        exit 1
    fi
    
    print_header "Redis Memory Information"
    docker exec "$REDIS_CONTAINER" redis-cli INFO memory | grep -E "(used_memory|maxmemory|mem_fragmentation|evicted_keys)"
}

# Show Redis configuration
show_config() {
    if ! check_redis_status; then
        print_error "Redis is not running"
        exit 1
    fi
    
    print_header "Redis Configuration"
    echo "Memory Settings:"
    docker exec "$REDIS_CONTAINER" redis-cli CONFIG GET maxmemory*
    echo ""
    echo "Persistence Settings:"
    docker exec "$REDIS_CONTAINER" redis-cli CONFIG GET save
    docker exec "$REDIS_CONTAINER" redis-cli CONFIG GET appendonly
}

# Clear all Redis data
clear_data() {
    if ! check_redis_status; then
        print_error "Redis is not running"
        exit 1
    fi
    
    print_warning "This will clear ALL Redis data. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        docker exec "$REDIS_CONTAINER" redis-cli EVAL "return redis.call('del', unpack(redis.call('keys', '*')))" 0
        print_status "Redis data cleared"
    else
        print_status "Operation cancelled"
    fi
}

# Show Redis statistics
show_stats() {
    if ! check_redis_status; then
        print_error "Redis is not running"
        exit 1
    fi
    
    print_header "Redis Statistics"
    docker exec "$REDIS_CONTAINER" redis-cli INFO stats | grep -E "(total_commands|total_connections|evicted_keys|expired_keys|keyspace_hits|keyspace_misses)"
}

# Test Redis performance
test_performance() {
    if ! check_redis_status; then
        print_error "Redis is not running"
        exit 1
    fi
    
    print_header "Redis Performance Test"
    print_status "Running benchmark with 10000 requests..."
    docker exec "$REDIS_CONTAINER" redis-benchmark -n 10000 -q
}

# Show help
show_help() {
    echo "Redis Test Environment Utilities"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start       Start Redis test environment"
    echo "  stop        Stop Redis test environment"
    echo "  restart     Restart Redis test environment"
    echo "  status      Check Redis status"
    echo "  connect     Connect to Redis CLI"
    echo "  memory      Show memory information"
    echo "  config      Show Redis configuration"
    echo "  stats       Show Redis statistics"
    echo "  clear       Clear all Redis data (interactive)"
    echo "  benchmark   Run performance test"
    echo "  help        Show this help message"
}

# Main script logic
case "${1:-help}" in
    start)
        start_redis
        ;;
    stop)
        stop_redis
        ;;
    restart)
        restart_redis
        ;;
    status)
        check_redis_status
        ;;
    connect|cli)
        connect_redis
        ;;
    memory|mem)
        show_memory_info
        ;;
    config|conf)
        show_config
        ;;
    stats)
        show_stats
        ;;
    clear)
        clear_data
        ;;
    benchmark|perf)
        test_performance
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
