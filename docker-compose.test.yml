services:
  backend:
    container_name: backend
    build:
      context: ./backend
      target: production
    volumes:
      - ./backend/logs:/usr/src/app/logs
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=test
    depends_on:
      - mysql
      - redis
    env_file:
      - .env.test
    networks:
      - app-test-network

  admin-frontend:
    container_name: admin-frontend-test
    build:
      context: ./admin-frontend
      target: test
    volumes:
      - ./admin-frontend:/usr/src/app
      - /usr/src/app/node_modules
    ports:
      - "5173:5173"
    environment:
      - CHOKIDAR_USEPOLLING=true
    restart: unless-stopped
    networks:
      - app-test-network

  nginx:
    container_name: nginx-test
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d/test:/etc/nginx/conf.d  # 直接挂载到nginx的配置目录
      - /etc/letsencrypt:/etc/letsencrypt:ro  # 挂载整个Let's Encrypt目录（只读）
      - /var/www/html:/var/www/html  # 挂载宿主机的静态文件目录
    depends_on:
      - backend
      - admin-frontend
    networks:
      - app-test-network

  mysql:
    container_name: mysql
    image: mysql:8.0
    restart: unless-stopped
    env_file:
      - .env.test
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
      - MYSQL_DATABASE=${DB_NAME}
      - MYSQL_USER=${DB_USER}
      - MYSQL_PASSWORD=${DB_PASSWORD}
    volumes:
      - mysql_test_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci --default-time-zone='+08:00'
    networks:
      - app-test-network

  redis:
    container_name: redis
    image: redis:alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - ./redis/test:/usr/local/etc/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - app-test-network

networks:
  app-test-network:
    driver: bridge
    name: my-app-test-network

volumes:
  mysql_test_data:
  redis_test_data: 