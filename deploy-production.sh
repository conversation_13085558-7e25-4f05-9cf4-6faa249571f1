#!/bin/bash

# 生产环境部署脚本
set -e

echo "🚀 开始生产环境部署..."

# 1. 构建 backend 镜像
echo "📦 构建 backend 镜像..."
docker build -t moodplay-backend:latest --target production ./backend

# 2. 构建 admin-frontend 镜像
echo "📦 构建 admin-frontend 镜像..."
docker build -t moodplay-admin-frontend:latest --target production ./admin-frontend

# 3. 保存镜像为 tar 文件
echo "💾 保存镜像为 tar 文件..."
mkdir -p ./docker-images
docker save moodplay-backend:latest -o ./docker-images/moodplay-backend.tar
docker save moodplay-admin-frontend:latest -o ./docker-images/moodplay-admin-frontend.tar

echo "✅ 镜像构建完成！"
echo "📁 镜像文件保存在 ./docker-images/ 目录下"
echo ""
echo "📋 接下来的步骤："
echo "1. 将 docker-images/ 目录上传到生产服务器"
echo "2. 将 docker-compose.prod.yml 上传到生产服务器"
echo "3. 将 .env.production.server 重命名为 .env 并配置正确的环境变量"
echo "4. 在生产服务器上运行加载脚本"
echo ""
echo "🔧 生产服务器上的命令："
echo "# 加载镜像"
echo "docker load -i moodplay-backend.tar"
echo "docker load -i moodplay-admin-frontend.tar"
echo ""
echo "# 启动服务"
echo "docker-compose -f docker-compose.prod.yml up -d"
