# 生产环境部署指南

## 概述

本项目采用 Docker 容器化部署，包含以下服务：
- **Backend**: Node.js API 服务 (端口 3001)
- **Admin Frontend**: React 管理后台 (端口 5173)
- **MySQL**: 数据库服务 (端口 3306)
- **Redis**: 缓存服务 (端口 6379)

## 部署流程

### 1. 本地构建镜像

在本地项目根目录执行：

```bash
./deploy-production.sh
```

这个脚本会：
- 构建 backend 和 admin-frontend 的生产镜像
- 将镜像保存为 tar 文件到 `./docker-images/` 目录

### 2. 上传文件到生产服务器

将以下文件上传到生产服务器：

```
moodplay-production/
├── docker-images/
│   ├── moodplay-backend.tar
│   └── moodplay-admin-frontend.tar
├── docker-compose.prod.yml
├── load-and-start.sh
├── .env (从 .env.production.server 复制并配置)
└── mysql/
    └── init/  (如果有初始化 SQL 脚本)
```

### 3. 配置环境变量

在生产服务器上：

```bash
# 复制环境变量模板
cp .env.production.server .env

# 编辑环境变量，填入真实的配置值
vi .env
```

需要配置的关键变量：
- `DB_PASSWORD`: MySQL 密码
- `WECHAT_APP_ID` / `WECHAT_APP_SECRET`: 微信配置
- `JWT_SECRET`: JWT 密钥
- `OSS_*`: 阿里云 OSS 配置
- `CDN_DOMAIN`: CDN 域名
- `AI_BASE_URL`: AI 服务地址

### 4. 在生产服务器上部署

```bash
# 给脚本执行权限
chmod +x load-and-start.sh

# 执行部署
./load-and-start.sh
```

## 服务管理

### 查看服务状态
```bash
docker-compose -f docker-compose.prod.yml ps
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.prod.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f admin-frontend
```

### 重启服务
```bash
# 重启所有服务
docker-compose -f docker-compose.prod.yml restart

# 重启特定服务
docker-compose -f docker-compose.prod.yml restart backend
```

### 停止服务
```bash
docker-compose -f docker-compose.prod.yml down
```

### 更新服务
```bash
# 1. 上传新的镜像文件
# 2. 加载新镜像
docker load -i moodplay-backend.tar
docker load -i moodplay-admin-frontend.tar

# 3. 重新启动服务
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d
```

## 网络配置

服务使用独立的 Docker 网络 `moodplay-prod-network`，避免与客户现有服务冲突。

端口映射：
- Backend: `3000:3000`
- Admin Frontend: `5174:80`
- MySQL: `3307:3306` (避免冲突)
- Redis: `6380:6379` (避免冲突)

## 与客户 Nginx 集成

下一步需要在客户的 Nginx 配置中添加反向代理规则，将特定路径转发到我们的服务：

- API 请求 → Backend (localhost:3000)
- 管理后台 → Admin Frontend (localhost:5174)

## 注意事项

1. **端口冲突**: 已调整 MySQL 和 Redis 端口避免与客户服务冲突
2. **数据持久化**: MySQL 和 Redis 数据通过 Docker volumes 持久化
3. **日志管理**: Backend 配置了日志轮转，最大 10MB，保留 3 个文件
4. **健康检查**: 可以添加健康检查确保服务正常运行
5. **环境隔离**: 使用独立的 Docker 网络和容器名前缀避免冲突

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :3000
   ```

2. **容器启动失败**
   ```bash
   # 查看详细错误信息
   docker-compose -f docker-compose.prod.yml logs backend
   ```

3. **数据库连接失败**
   - 检查 MySQL 容器是否正常启动
   - 验证环境变量配置
   - 检查网络连接

4. **镜像加载失败**
   ```bash
   # 检查镜像文件完整性
   docker load -i moodplay-backend.tar
   ```
