# MySQL 生产环境配置指南

## 概述

本指南提供了为 Moodplay 项目配置生产级 MySQL 数据库的完整方案。配置重点关注数据安全、基本性能优化和备份恢复能力，适合 MVP 产品的生产环境需求。

## 主要改进

### 1. 数据持久化和安全性
- ✅ 启用二进制日志 (binlog) 用于数据恢复
- ✅ 配置事务日志同步确保数据不丢失
- ✅ 设置日志保留期限 (7天)
- ✅ 启用慢查询日志用于性能监控

### 2. 备份策略
- ✅ 自动化每日备份脚本
- ✅ 备份文件压缩和清理
- ✅ 数据库恢复脚本
- ✅ 定时任务配置

### 3. 监控和维护
- ✅ 健康检查脚本
- ✅ 性能监控
- ✅ 错误日志监控

## 文件结构

```
moodplay/
├── docker-compose.prod.yml          # 更新的生产环境配置
├── mysql/
│   └── conf.d/
│       └── production.cnf           # MySQL 生产环境配置
└── scripts/
    ├── mysql-backup.sh              # 数据库备份脚本
    ├── mysql-restore.sh             # 数据库恢复脚本
    ├── mysql-health-check.sh        # 健康检查脚本
    └── setup-mysql-cron.sh          # 定时备份设置脚本
```

## 部署步骤

### 1. 更新生产环境

在生产服务器上执行以下步骤：

```bash
# 1. 停止当前服务
docker compose -f docker-compose.prod.yml down

# 2. 备份当前数据（重要！）
docker run --rm -v moodplay_mysql_data:/data -v $(pwd):/backup alpine tar czf /backup/mysql_data_backup_$(date +%Y%m%d).tar.gz -C /data .

# 3. 拉取最新代码
git pull origin main

# 4. 启动更新后的服务
docker compose -f docker-compose.prod.yml up -d
```

### 2. 验证配置

```bash
# 检查容器状态
docker compose -f docker-compose.prod.yml ps

# 检查MySQL配置是否生效
docker exec mysql mysql -u admin -p -e "SHOW VARIABLES LIKE 'log_bin';"
docker exec mysql mysql -u admin -p -e "SHOW VARIABLES LIKE 'innodb_buffer_pool_size';"
```

### 3. 设置自动备份

```bash
# 设置定时备份（需要root权限）
sudo ./scripts/setup-mysql-cron.sh --setup

# 测试备份功能
sudo ./scripts/setup-mysql-cron.sh --test
```

### 4. 运行健康检查

```bash
# 加载环境变量并运行健康检查
source .env.production
./scripts/mysql-health-check.sh
```

## 配置详解

### MySQL 配置亮点

1. **数据安全配置**
   ```ini
   innodb_flush_log_at_trx_commit = 1  # 每次事务提交都写入磁盘
   sync_binlog = 1                     # 每次事务提交都同步binlog
   log-bin = mysql-bin                 # 启用二进制日志
   ```

2. **性能优化配置**
   ```ini
   innodb_buffer_pool_size = 256M      # 缓冲池大小
   max_connections = 200               # 最大连接数
   slow_query_log = 1                  # 启用慢查询日志
   ```

3. **字符集配置**
   ```ini
   character-set-server = utf8mb4      # 支持完整的UTF-8
   collation-server = utf8mb4_unicode_ci
   ```

### 备份策略

- **频率**: 每天凌晨2点自动备份
- **保留期**: 7天
- **压缩**: 自动压缩备份文件节省空间
- **验证**: 备份后自动验证文件完整性

## 使用指南

### 手动备份

```bash
# 加载环境变量
source .env.production

# 执行备份
./scripts/mysql-backup.sh
```

### 数据恢复

```bash
# 查看可用备份
./scripts/mysql-restore.sh

# 恢复指定备份
./scripts/mysql-restore.sh moodplay_backup_20240131_120000.sql.gz
```

### 健康检查

```bash
# 完整健康检查
source .env.production
./scripts/mysql-health-check.sh
```

## 监控建议

### 关键指标监控

1. **磁盘空间**: 确保数据目录有足够空间
2. **连接数**: 监控活跃连接数量
3. **慢查询**: 定期检查慢查询日志
4. **备份状态**: 确保每日备份正常执行

### 日志位置

- **错误日志**: `/var/log/mysql/error.log`
- **慢查询日志**: `/var/log/mysql/slow.log`
- **二进制日志**: `/var/lib/mysql/mysql-bin.*`
- **备份日志**: `/var/log/mysql-backup.log`

## 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 检查日志
   docker logs mysql
   
   # 检查配置文件语法
   docker exec mysql mysqld --help --verbose
   ```

2. **备份失败**
   ```bash
   # 检查权限
   docker exec mysql ls -la /backup
   
   # 检查磁盘空间
   docker exec mysql df -h
   ```

3. **性能问题**
   ```bash
   # 检查慢查询
   docker exec mysql tail -f /var/log/mysql/slow.log
   
   # 检查连接状态
   docker exec mysql mysql -u admin -p -e "SHOW PROCESSLIST;"
   ```

## 安全建议

1. **网络安全**
   - 仅在必要时暴露3306端口
   - 使用强密码
   - 定期更换密码

2. **访问控制**
   - 限制root用户远程访问
   - 为应用创建专用数据库用户
   - 定期审查用户权限

3. **备份安全**
   - 备份文件加密存储
   - 定期测试恢复流程
   - 异地备份存储

## 扩展建议

当业务增长时，可以考虑以下扩展：

1. **主从复制**: 提高读性能和数据冗余
2. **连接池**: 优化应用连接管理
3. **监控系统**: 集成 Prometheus + Grafana
4. **自动化运维**: 使用 Ansible 或 Terraform

## 总结

此配置为 MVP 产品提供了：
- ✅ 数据安全保障
- ✅ 自动化备份恢复
- ✅ 基本性能优化
- ✅ 运维监控工具

配置简单可靠，适合小到中型应用的生产环境需求。
