# 开发环境
FROM node:18-alpine as development

WORKDIR /usr/src/app

COPY package*.json ./
COPY yarn.lock ./

# 确保安装所有依赖，包括开发依赖
RUN yarn install --frozen-lockfile

COPY . .

# 创建日志目录
RUN mkdir -p logs && chmod 777 logs

RUN yarn build

# 生产环境
FROM node:18-alpine as production

ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

WORKDIR /usr/src/app

COPY package*.json ./
COPY yarn.lock ./

RUN yarn install --production

COPY --from=development /usr/src/app/dist ./dist

# 复制微信支付证书文件到容器中
COPY --from=development /usr/src/app/apiclient_cert.pem ./apiclient_cert.pem
COPY --from=development /usr/src/app/apiclient_key.pem ./apiclient_key.pem

# 创建日志目录
RUN mkdir -p logs && chmod 777 logs

CMD ["node", "dist/index"]