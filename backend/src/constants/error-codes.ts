/**
 * 系统错误码常量
 * 用于统一管理各种错误状态码
 */

// 系统维护相关错误码
export const ERROR_CODES = {
  // 系统维护状态 - 使用特殊的错误码，便于小程序端识别
  SYSTEM_MAINTENANCE: 503001,
  
  // 其他常用错误码
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

// 错误消息映射
export const ERROR_MESSAGES = {
  [ERROR_CODES.SYSTEM_MAINTENANCE]: '系统正在维护中',
  [ERROR_CODES.UNAUTHORIZED]: '未授权访问',
  [ERROR_CODES.FORBIDDEN]: '禁止访问',
  [ERROR_CODES.NOT_FOUND]: '资源不存在',
  [ERROR_CODES.INTERNAL_ERROR]: '服务器内部错误',
  [ERROR_CODES.SERVICE_UNAVAILABLE]: '服务不可用',
} as const;
