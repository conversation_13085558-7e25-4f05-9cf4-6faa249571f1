import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../config/sequelize';

export interface UserDivinationAttributes {
  id: number;
  userId: number;
  inputText: string;
  aiResponse: string;
  success?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface UserDivinationCreationAttributes extends Optional<UserDivinationAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

class UserDivination extends Model<UserDivinationAttributes, UserDivinationCreationAttributes> implements UserDivinationAttributes {
  public id!: number;
  public userId!: number;
  public inputText!: string;
  public aiResponse!: string;
  public success!: boolean;
  public createdAt!: Date;
  public updatedAt!: Date;

  // 关联方法
  public static associate(models: any) {
    UserDivination.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });
  }
}

UserDivination.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    inputText: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    aiResponse: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    success: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'UserDivination',
    tableName: 'user_divinations',
    timestamps: false,
    underscored: true,
  }
);

export default UserDivination; 