import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../config/sequelize';

 export interface UserAttributes {
  id: number;
  openid: string;
  unionid: string | null;
  phone: string | null;
  username: string | null;
  avatarKey: string | null;
  onboarding: boolean | null;
  birthDate: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

interface UserCreationAttributes extends Optional<UserAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
  public id!: number;
  public openid!: string;
  public unionid!: string | null;
  public phone!: string | null;
  public username!: string | null;
  public avatarKey!: string | null;
  public onboarding!: boolean;
  public birthDate!: Date | null;
  public createdAt!: Date;
  public updatedAt!: Date;

  // 关联方法将在后面定义
  public static associate(models: any) {
    User.hasMany(models.PlaySession, { foreignKey: 'userId' });
    User.hasMany(models.Invitation, {
      foreignKey: 'inviterId',
      as: 'sentInvitations',
    });
    User.hasMany(models.Invitation, {
      foreignKey: 'inviteeId',
      as: 'receivedInvitations',
    });
    User.hasMany(models.UserFeedback, {
      foreignKey: 'user_id',
      as: 'feedbacks',
    });
    User.hasMany(models.UserDivination, {
      foreignKey: 'userId',
      as: 'divinations',
    });
    User.hasMany(models.UserPaidMembership, {
      foreignKey: 'userId',
      as: 'paidMemberships',
    });
    User.hasMany(models.MembershipOrder, {
      foreignKey: 'userId',
      as: 'membershipOrders',
    });
    User.hasMany(models.UserReward, {
      foreignKey: 'userId',
      as: 'rewards',
    });
  }
}

User.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    openid: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
    },
    unionid: {
      type: DataTypes.STRING(100),
      allowNull: true,
      unique: true,
    },
    phone: {
      type: DataTypes.STRING(30),
      allowNull: true,
      unique: true,
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: true,
    },
    avatarKey: {
      type: DataTypes.STRING(256),
      allowNull: true,
    },
    birthDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    onboarding: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'User',
    tableName: 'users',
    timestamps: false,
    underscored: true,
  }
);

export default User;
