import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../config/sequelize';
import PlaySession from './play-session.model';
import User from './user.model';

interface DetectedEmotionAttributes {
  id: number;
  playSessionId: number;
  userId: number;
  emotionKey: string;
  emotionDescription: string | null;
  symbol: number;
  isSelected: boolean;
  createdAt: Date;
}

interface DetectedEmotionCreationAttributes extends Optional<DetectedEmotionAttributes, 'id' | 'createdAt' | 'isSelected'> {}

export class DetectedEmotion extends Model<DetectedEmotionAttributes, DetectedEmotionCreationAttributes> implements DetectedEmotionAttributes {
  public id!: number;
  public playSessionId!: number;
  public userId!: number;
  public emotionKey!: string;
  public emotionDescription!: string | null;
  public symbol!: number;
  public isSelected!: boolean;
  public createdAt!: Date;

  // 关联方法
  public static associate(models: any) {
    DetectedEmotion.belongsTo(models.PlaySession, { foreignKey: 'playSessionId' });
  }
}

DetectedEmotion.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    playSessionId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: PlaySession,
        key: 'id',
      },
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
    },
    emotionKey: {
      type: DataTypes.STRING(10),
      allowNull: false,
    },
    emotionDescription: {
      type: DataTypes.STRING(30),
      allowNull: true,
    },
    symbol: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    isSelected: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'DetectedEmotion',
    tableName: 'detected_emotions',
    timestamps: false,
    underscored: true,
  }
);

export default DetectedEmotion; 