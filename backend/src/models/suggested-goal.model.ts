import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../config/sequelize';
import PlaySession from './play-session.model';
import User from './user.model';

interface SuggestedGoalAttributes {
  id: number;
  playSessionId: number;
  userId: number;
  goalKey: string;
  goalDescription: string | null;
  goalLongDescription: string | null;
  goalImageKey: string | null;
  isSelected: boolean;
  createdAt: Date;
}

interface SuggestedGoalCreationAttributes extends Optional<SuggestedGoalAttributes, 'id' | 'createdAt' | 'isSelected'> {}

class SuggestedGoal extends Model<SuggestedGoalAttributes, SuggestedGoalCreationAttributes> implements SuggestedGoalAttributes {
  public id!: number;
  public playSessionId!: number;
  public userId!: number;
  public goalKey!: string;
  public goalDescription!: string | null;
  public goalLongDescription!: string | null;
  public goalImageKey!: string | null;
  public isSelected!: boolean;
  public createdAt!: Date;

  // 关联方法
  public static associate(models: any) {
    SuggestedGoal.belongsTo(models.PlaySession, { foreignKey: 'playSessionId' });
  }
}

SuggestedGoal.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    playSessionId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: PlaySession,
        key: 'id',
      },
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
    },
    goalKey: {
      type: DataTypes.STRING(20),
      allowNull: false,
    },
    goalDescription: {
      type: DataTypes.STRING(30),
      allowNull: true,
    },
    goalLongDescription: {
      type: DataTypes.STRING(30),
      allowNull: true,
    },
    goalImageKey: {
      type: DataTypes.STRING(512),
      allowNull: true,
    },
    isSelected: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'SuggestedGoal',
    tableName: 'suggested_goals',
    timestamps: false,
    underscored: true,
  }
);

export default SuggestedGoal; 