import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../config/sequelize';
import { MembershipPlanKey, MembershipRenewalType } from '../enums/membership.enum';

export interface MembershipPlanAttributes {
  id: number;
  name: string;
  planKey: MembershipPlanKey;
  durationDays: number | null;
  price: number;
  renewalType: MembershipRenewalType;
  description: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface MembershipPlanCreationAttributes extends Optional<MembershipPlanAttributes, 'id' | 'description' | 'isActive' | 'createdAt' | 'updatedAt' | 'durationDays'> {}

class MembershipPlan extends Model<MembershipPlanAttributes, MembershipPlanCreationAttributes> implements MembershipPlanAttributes {
  public id!: number;
  public name!: string;
  public planKey!: MembershipPlanKey;
  public durationDays!: number | null;
  public price!: number;
  public renewalType!: MembershipRenewalType;
  public description!: string | null;
  public isActive!: boolean;
  public createdAt!: Date;
  public updatedAt!: Date;

  public static associate(models: any) {
    MembershipPlan.hasMany(models.UserPaidMembership, { foreignKey: 'planId', as: 'userPaidMemberships' });
    MembershipPlan.hasMany(models.MembershipOrder, { foreignKey: 'planId', as: 'orders' });
  }
}

MembershipPlan.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    planKey: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
    },
    durationDays: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    renewalType: {
      type: DataTypes.ENUM(MembershipRenewalType.NONE, MembershipRenewalType.AUTO_RENEW, MembershipRenewalType.LIFETIME),
      allowNull: false,
      defaultValue: MembershipRenewalType.NONE,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'MembershipPlan',
    tableName: 'membership_plans',
    timestamps: false, 
    underscored: true,
  }
);

export default MembershipPlan;
