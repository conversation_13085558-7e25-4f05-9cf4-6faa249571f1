import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../config/sequelize';
import User from './user.model';
import MembershipPlan from './membership-plan.model';

export interface MembershipOrderAttributes {
  id: number;
  orderNo: string;
  userId: number;
  planId: number;
  amount: number;
  currency: string;
  status: 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED' | 'CLOSED';
  paymentMethod: string | null;
  transactionId: string | null;
  paidAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

interface MembershipOrderCreationAttributes extends Optional<MembershipOrderAttributes, 'id' | 'currency' | 'status' | 'paymentMethod' | 'transactionId' | 'paidAt' | 'createdAt' | 'updatedAt'> {}

class MembershipOrder extends Model<MembershipOrderAttributes, MembershipOrderCreationAttributes> implements MembershipOrderAttributes {
  public id!: number;
  public orderNo!: string;
  public userId!: number;
  public planId!: number;
  public amount!: number;
  public currency!: string;
  public status!: 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED' | 'CLOSED';
  public paymentMethod!: string | null;
  public transactionId!: string | null;
  public paidAt!: Date | null;
  public createdAt!: Date;
  public updatedAt!: Date;

  public static associate(models: any) {
    MembershipOrder.belongsTo(models.User, { foreignKey: 'userId', onDelete: 'CASCADE', as: 'user' });
    MembershipOrder.belongsTo(models.MembershipPlan, { foreignKey: 'planId', as: 'plan' });
  }
}

MembershipOrder.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    orderNo: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    planId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: MembershipPlan,
        key: 'id',
      },
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING(10),
      defaultValue: 'CNY',
    },
    status: {
      type: DataTypes.ENUM('PENDING', 'PAID', 'FAILED', 'REFUNDED', 'CLOSED'),
      defaultValue: 'PENDING',
    },
    paymentMethod: {
      type: DataTypes.STRING(50),
      allowNull: true,
    },
    transactionId: {
      type: DataTypes.STRING(100),
      allowNull: true,
      unique: true, 
    },
    paidAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'MembershipOrder',
    tableName: 'membership_orders',
    timestamps: false,
    underscored: true,
  }
);

export default MembershipOrder;
