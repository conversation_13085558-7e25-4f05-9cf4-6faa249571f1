import { Model, DataTypes, Association, Sequelize } from 'sequelize';
import User from './user.model';
import sequelize from '../config/sequelize';

export interface UserFeedbackAttributes {
  id?: number;
  userId: number;
  rating: number; // 1-5星评分
  content?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

class UserFeedback extends Model<UserFeedbackAttributes> implements UserFeedbackAttributes {
  public id!: number;
  public userId!: number;
  public rating!: number;
  public content?: string;
  public createdAt!: Date;
  public updatedAt!: Date;

  // 关联属性
  public readonly user?: User;

  public static associations: {
    user: Association<UserFeedback, User>;
  };

  public static associate(models: any) {
    UserFeedback.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user',
      onDelete: 'CASCADE'
    });
  }
}

UserFeedback.init({
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  rating: {
    type: DataTypes.TINYINT,
    allowNull: false,
    validate: {
      min: 1,
      max: 5,
      isInt: true
    }
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  sequelize,
  modelName: 'UserFeedback',
  tableName: 'user_feedbacks',
  timestamps: true,
  underscored: true
});

export default UserFeedback; 