import { DataTypes, Model, Optional } from 'sequelize';
import sequelize from '../config/sequelize';

interface UserRewardAttributes {
  id: number;
  user_id: number;
  reward_type_id: number;
  reward_days: number;
  status: 'GRANTED' | 'MERGED';
  expire_date: Date;
  notes?: string;
  merge_at?: Date;
  merge_reason?: string;
  created_at: Date;
  updated_at: Date;
}

interface UserRewardCreationAttributes extends Optional<UserRewardAttributes, 'id' | 'status' | 'notes' | 'merge_at' | 'merge_reason' | 'created_at' | 'updated_at'> {}

class UserReward extends Model<UserRewardAttributes, UserRewardCreationAttributes> implements UserRewardAttributes {
  public id!: number;
  public user_id!: number;
  public reward_type_id!: number;
  public reward_days!: number;
  public status!: 'GRANTED' | 'MERGED';
  public expire_date!: Date;
  public notes?: string;
  public merge_at?: Date;
  public merge_reason?: string;
  public created_at!: Date;
  public updated_at!: Date;

  static associate(models: any) {
    UserReward.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
    
    UserReward.belongsTo(models.RewardType, {
      foreignKey: 'reward_type_id',
      as: 'reward_type'
    });
  }
}

UserReward.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    reward_type_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'reward_types',
        key: 'id',
      },
    },
    reward_days: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM('GRANTED', 'MERGED'),
      allowNull: false,
      defaultValue: 'GRANTED',
    },
    expire_date: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    merge_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    merge_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'UserReward',
    tableName: 'user_rewards',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['reward_type_id'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['expire_date'],
      },
      {
        fields: ['user_id', 'status'],
      },
      {
        fields: ['user_id', 'expire_date'],
      },
      {
        fields: ['user_id', 'reward_type_id'],
      },
    ],
  }
);

export default UserReward; 