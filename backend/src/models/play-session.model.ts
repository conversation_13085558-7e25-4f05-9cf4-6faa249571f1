import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../config/sequelize';
import { PlaySessionStatus } from '../enums';
import User from './user.model';

interface PlaySessionAttributes {
  id: number;
  userId: number;
  inputText: string;
  experienceMode: string;
  createdAt: Date;
  status: string;
  respQuote?: string;
  respAction?: string;
  respEmpathy?: string;
  respWorkflow?: string;
}

interface PlaySessionCreationAttributes extends Optional<PlaySessionAttributes, 'id' | 'createdAt' | 'experienceMode'> {}

class PlaySession extends Model<PlaySessionAttributes, PlaySessionCreationAttributes> implements PlaySessionAttributes {
  public id!: number;
  public userId!: number;
  public inputText!: string;
  public experienceMode!: string;
  public createdAt!: Date;
  public status!: string;
  public respQuote?: string;
  public respAction?: string;
  public respEmpathy?: string;
  public respWorkflow?: string;

  // 关联方法
  public static associate(models: any) {
    PlaySession.belongsTo(models.User, { foreignKey: 'userId' });
    PlaySession.hasMany(models.DetectedEmotion, { 
      foreignKey: 'playSessionId',
      as: 'detectedEmotions'
    });
    PlaySession.hasMany(models.SuggestedGoal, { 
      foreignKey: 'playSessionId',
      as: 'suggestedGoals'
    });
  }
}

PlaySession.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
    },
    inputText: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    experienceMode: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'text',
    },
    respQuote: {
      type: DataTypes.STRING(512),
      allowNull: true,
    },
    respAction: {
      type: DataTypes.STRING(512),
      allowNull: true,
    },
    respEmpathy: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    respWorkflow: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    status: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: PlaySessionStatus.ACTIVE,
    },
  },
  {
    sequelize,
    modelName: 'PlaySession',
    tableName: 'play_sessions',
    timestamps: false,
    underscored: true,
  }
);

export default PlaySession; 