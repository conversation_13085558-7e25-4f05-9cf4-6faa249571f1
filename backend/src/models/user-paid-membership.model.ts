import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../config/sequelize';

export interface UserPaidMembershipAttributes {
  id: number;
  userId: number;
  planId: number;
  startDate: Date;
  expireDate: Date;
  isRenewalActive: boolean;
  status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED';
  orderId?: number;
  createdAt: Date;
  updatedAt: Date;
}

interface UserPaidMembershipCreationAttributes extends Optional<UserPaidMembershipAttributes, 'id' | 'isRenewalActive' | 'status' | 'orderId' | 'createdAt' | 'updatedAt'> {}

class UserPaidMembership extends Model<UserPaidMembershipAttributes, UserPaidMembershipCreationAttributes> implements UserPaidMembershipAttributes {
  public id!: number;
  public userId!: number;
  public planId!: number;
  public startDate!: Date;
  public expireDate!: Date;
  public isRenewalActive!: boolean;
  public status!: 'ACTIVE' | 'EXPIRED' | 'CANCELLED';
  public orderId?: number;
  public createdAt!: Date;
  public updatedAt!: Date;

  public static associate(models: any) {
    UserPaidMembership.belongsTo(models.User, { 
      foreignKey: 'userId', 
      onDelete: 'CASCADE', 
      as: 'user' 
    });
    UserPaidMembership.belongsTo(models.MembershipPlan, { 
      foreignKey: 'planId', 
      as: 'plan' 
    });
    UserPaidMembership.belongsTo(models.MembershipOrder, { 
      foreignKey: 'orderId', 
      as: 'order' 
    });
  }
}

UserPaidMembership.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    planId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'membership_plans',
        key: 'id',
      },
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    expireDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    isRenewalActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    status: {
      type: DataTypes.ENUM('ACTIVE', 'EXPIRED', 'CANCELLED'),
      allowNull: false,
      defaultValue: 'ACTIVE',
    },
    orderId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'membership_orders',
        key: 'id',
      },
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'UserPaidMembership',
    tableName: 'user_paid_memberships',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    underscored: true,
  }
);

export default UserPaidMembership; 