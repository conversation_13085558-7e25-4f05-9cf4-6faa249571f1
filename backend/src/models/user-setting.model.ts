import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../config/sequelize';

export interface UserSettingAttributes {
  id: number;
  userId: number;
  voiceName: string | null;
  language: string;
  createdAt: Date;
  updatedAt: Date;
}

interface UserSettingCreationAttributes extends Optional<UserSettingAttributes, 'id' | 'voiceName' | 'language' | 'createdAt' | 'updatedAt'> {}

class UserSetting extends Model<UserSettingAttributes, UserSettingCreationAttributes> implements UserSettingAttributes {
  public id!: number;
  public userId!: number;
  public voiceName!: string | null;
  public language!: string;
  public createdAt!: Date;
  public updatedAt!: Date;

  public static associate(models: any) {
    UserSetting.belongsTo(models.User, { foreignKey: 'userId', as: 'user' });
  }
}

UserSetting.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    voiceName: {
      type: DataTypes.STRING(32),
      allowNull: true,
    },
    language: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'zh-CN',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'UserSetting',
    tableName: 'user_settings',
    timestamps: false,
    underscored: true,
  }
);

export default UserSetting;