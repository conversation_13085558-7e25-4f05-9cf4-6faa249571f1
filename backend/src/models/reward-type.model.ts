import { DataTypes, Model, Optional } from 'sequelize';
import sequelize from '../config/sequelize';

interface RewardTypeAttributes {
  id: number;
  type_key: string;
  name: string;
  description?: string;
  reward_days: number;
  is_repeatable: boolean;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

interface RewardTypeCreationAttributes extends Optional<RewardTypeAttributes, 'id' | 'description' | 'is_repeatable' | 'is_active' | 'created_at' | 'updated_at'> {}

class RewardType extends Model<RewardTypeAttributes, RewardTypeCreationAttributes> implements RewardTypeAttributes {
  public id!: number;
  public type_key!: string;
  public name!: string;
  public description?: string;
  public reward_days!: number;
  public is_repeatable!: boolean;
  public is_active!: boolean;
  public created_at!: Date;
  public updated_at!: Date;

  static associate(models: any) {
    RewardType.hasMany(models.UserReward, {
      foreignKey: 'reward_type_id',
      as: 'user_rewards'
    });
  }
}

RewardType.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    type_key: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    reward_days: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    is_repeatable: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'RewardType',
    tableName: 'reward_types',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['type_key'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['is_repeatable'],
      },
    ],
  }
);

export default RewardType; 