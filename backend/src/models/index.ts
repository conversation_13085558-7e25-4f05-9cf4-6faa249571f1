import User from './user.model';
import PlaySession from './play-session.model';
import DetectedEmotion from './detected-emotion.model';
import SuggestedGoal from './suggested-goal.model';

import UserSetting from './user-setting.model';
import MembershipPlan from './membership-plan.model';
import UserPaidMembership from './user-paid-membership.model';
import MembershipOrder from './membership-order.model';
import Invitation from './invitation.model';
import RewardType from './reward-type.model';
import UserReward from './user-reward.model';
import UserFeedback from './user-feedback.model';
import UserDivination from './user-divination.model';

const models = {
  User,
  PlaySession,
  DetectedEmotion,
  SuggestedGoal,
  Invitation,
  UserSetting,
  MembershipPlan,
  UserPaidMembership,
  MembershipOrder,
  RewardType,
  UserReward,
  UserFeedback,
  UserDivination,
};

// 设置模型之间的关联关系
User.associate(models);
PlaySession.associate(models);
DetectedEmotion.associate({ PlaySession });
SuggestedGoal.associate({ PlaySession });
Invitation.associate(models);
UserSetting.associate(models);
MembershipPlan.associate(models);
UserPaidMembership.associate(models);
MembershipOrder.associate(models);
RewardType.associate(models);
UserReward.associate(models);
UserFeedback.associate(models);
UserDivination.associate(models);

export {
  User,
  PlaySession,
  DetectedEmotion,
  SuggestedGoal,
  Invitation,
  UserSetting,
  MembershipPlan,
  UserPaidMembership,
  MembershipOrder,
  RewardType,
  UserReward,
  UserFeedback,
  UserDivination,
}; 