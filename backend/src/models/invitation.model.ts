import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../config/sequelize';
import User from './user.model'; // 引入User模型用于关联

export interface InvitationAttributes {
  id: number;
  inviterId: number;
  inviteeId: number;
  createdAt: Date;
  updatedAt: Date;
}

interface InvitationCreationAttributes extends Optional<InvitationAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

class Invitation extends Model<InvitationAttributes, InvitationCreationAttributes> implements InvitationAttributes {
  public id!: number;
  public inviterId!: number;
  public inviteeId!: number;
  // 时间戳
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联对象
  public readonly inviter?: User;
  public readonly invitee?: User;

  public static associate(models: any) {
    Invitation.belongsTo(models.User, {
      foreignKey: 'inviterId',
      as: 'inviter', // 邀请者
    });
    Invitation.belongsTo(models.User, {
      foreignKey: 'inviteeId',
      as: 'invitee', // 被邀请者
    });
  }
}

Invitation.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    inviterId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users', // 表名
        key: 'id',
      },
    },
    inviteeId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users', // 表名
        key: 'id',
      },
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'Invitation',
    tableName: 'invitations',
    timestamps: true,
    underscored: true,
  }
);

export default Invitation;