import { Sequelize } from 'sequelize';
import config from './index';

const sequelize = new Sequelize(
  config.database.database || '',
  config.database.user || '',
  config.database.password || '',
  {
    host: config.database.host,
    dialect: 'mysql',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    define: {
      underscored: true
    }
  }
);

export default sequelize; 