import dotenv from 'dotenv';

dotenv.config({ 
  path: process.env.NODE_ENV === 'development' ? `../.env` : `../.env.${process.env.NODE_ENV}` 
});

const config = {

  wechat: {
    appId: process.env.WECHAT_APP_ID || '',
    appSecret: process.env.WECHAT_APP_SECRET || '',
    tokenExpiresIn: 7 * 24 * 60 * 60 * 1000, // 7天
  },
  database: {
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: parseInt(process.env.DB_PORT || '5432'),
  }
};

const validateConfig = () => {
  const requiredEnvVars = [
    'JWT_SECRET',
    'WECHAT_APP_ID',
    'WECHAT_APP_SECRET'
  ];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`缺少必要的环境变量: ${envVar}`);
    }
  }
};

// 在应用启动时验证配置
validateConfig();

export default config;