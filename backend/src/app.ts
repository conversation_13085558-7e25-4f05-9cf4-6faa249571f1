import sequelize from './config/sequelize';
import './models'; // 导入所有模型以确保关联关系被设置
import logger from './utils/logger';


async function startServer() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    logger.info('数据库连接成功');
    
    // 如果需要，可以同步模型到数据库（开发环境使用）
    // await sequelize.sync({ alter: true });
    
    // 启动服务器
    // ...
  } catch (error) {
    logger.error('无法连接到数据库:', error);
    process.exit(1);
  }
}

startServer(); 