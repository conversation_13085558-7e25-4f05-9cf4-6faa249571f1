import { AppError } from '../middlewares/error.middleware';
import { DetectedEmotion } from '../models/detected-emotion.model';
import PlaySession from '../models/play-session.model';
import sequelize from '../config/sequelize';
import logger from '../utils/logger';

export class EmotionService {
  
  /**
   * 批量更新用户选择的情绪
   * @param playSessionId 会话ID
   * @param selectedEmotionIds 选中的情绪ID数组
   */
  static async updateMultipleEmotions(playSessionId: number, selectedEmotionIds: number[]):Promise<DetectedEmotion[]> {
    logger.info(`[EmotionService] Updating multiple emotions for playSessionId: ${playSessionId}, selectedEmotionIds: ${selectedEmotionIds}`);
    const transaction = await sequelize.transaction();
    
    try{
      // 先查询当前已选中的情绪ID
      const currentSelectedEmotions = await DetectedEmotion.findAll({
        where: { 
          playSessionId,
          isSelected: true 
        },
        attributes: ['id'],
        transaction
      });
      
      const currentSelectedIds = currentSelectedEmotions.map(emotion => emotion.id).sort();
      const newSelectedIds = [...selectedEmotionIds].sort();
      
      // 比较选择是否发生变化
      const hasChanged = currentSelectedIds.length !== newSelectedIds.length || 
                        !currentSelectedIds.every((id, index) => id === newSelectedIds[index]);
      
      // 先将该会话的所有情绪设为未选中
      await DetectedEmotion.update(
        { isSelected: false },
        { 
          where: { playSessionId },
          transaction
        }
      );
      
      // 如果有选中的情绪，则更新为选中状态
      if (selectedEmotionIds.length > 0) {
        await DetectedEmotion.update(
          { isSelected: true },
          { 
            where: { 
              id: selectedEmotionIds,
              playSessionId
            },
            transaction
          }
        );
      }
      
      // 只有在选择发生变化时才清空respEmpathy
      if (hasChanged) {
        await PlaySession.update(
          { respEmpathy: '', respWorkflow: '' },
          { 
            where: { id: playSessionId },
            transaction
          }
        );
      }
      
      // 返回更新后的情绪列表
      const result = await DetectedEmotion.findAll({
        where: { playSessionId },
        order: [['id', 'ASC']],
        transaction
      });
      
      await transaction.commit();
      return result;
    }catch(error){
      await transaction.rollback();
      throw new AppError('选择情绪失败', 500);
    }
  }
  
  /**
   * 获取会话的所有情绪
   * @param playSessionId 会话ID
   */
  static async getEmotionsByPlaySession(playSessionId: number) {
    return await DetectedEmotion.findAll({
      where: { playSessionId },
      order: [['id', 'ASC']]
    });
  }
}