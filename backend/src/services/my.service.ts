import { MyConfig, MyInfo } from "../types/my.type";
import { OssService } from "./oss.service";
import { UserService } from "./user.service";

export class MyService {

      static async getInfo(userId: number): Promise<MyInfo> {
            const config = await OssService.getObjectJsonContentWithCache<MyConfig>('my');
            config.membershipCard.cardBackgroundImage = OssService.generateResourceUrl(config.membershipCard.cardBackgroundImage);
            config.membershipCard.cardExpiredBackgroundImage = OssService.generateResourceUrl(config.membershipCard.cardExpiredBackgroundImage);
            config.membershipCard.cardLifetimeBackgroundImage = OssService.generateResourceUrl(config.membershipCard.cardLifetimeBackgroundImage);
            config.membershipCard.cardTextImage = OssService.generateResourceUrl(config.membershipCard.cardTextImage);
            config.rightArrowIcon = OssService.generateResourceUrl(config.rightArrowIcon);
            config.defaultAvatar = OssService.generateResourceUrl(config.defaultAvatar);
            // config.aboutPdfLink = OssService.generateUrl(config.aboutPdfLink);
            // config.systemPdfLink = OssService.generateUrl(config.systemPdfLink);
            // config.aboutPdfImage = OssService.generateResourceUrl(config.aboutPdfImage);
            // config.systemPdfImage = OssService.generateResourceUrl(config.systemPdfImage);
            const userInfo = await UserService.getUserFullProfile(userId);
            return {
                  config,
                  userInfo
            };
      }
}
