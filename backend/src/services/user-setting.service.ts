import UserSetting from '../models/user-setting.model';
import logger from '../utils/logger';

export class UserSettingService {

      static async updateVoiceSetting(userId: number, voiceName: string ) {
            const userSetting = await UserSetting.findOne({ where: { userId } });
            if(userSetting){
                  userSetting.voiceName = voiceName;
                  await userSetting.save();
            }else{
                  await UserSetting.create({ userId, voiceName });
            }
            return userSetting;
      }

      static async getSetting(userId: number) {
            logger.info(`[UserSettingService] Getting setting for userId: ${userId}`);
            const userSetting = await UserSetting.findOne({ where: { userId } });
            return userSetting;
      }
}
export default new UserSettingService();