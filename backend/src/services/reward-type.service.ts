import { RewardType } from '../models';
import { RedisService } from './redis.service';

export class RewardTypeService {
  private static readonly CACHE_TTL = 3600; // 1小时缓存
  private static readonly CACHE_KEY_PREFIX = 'reward_type:';

  /**
   * 根据类型键获取奖励类型（带缓存）
   */
  static async getByTypeKey(typeKey: string): Promise<RewardType | null> {
    const cacheKey = `${this.CACHE_KEY_PREFIX}type_key:${typeKey}`;
    
    // 尝试从缓存获取
    const cached = await RedisService.get(cacheKey);
    if (cached) {
      try {
        const data = JSON.parse(cached);
        // 重新创建RewardType实例
        return RewardType.build(data);
      } catch (error) {
        console.error('解析缓存数据失败:', error);
        // 缓存数据损坏，删除缓存
        await RedisService.del(cacheKey);
      }
    }

    // 从数据库查询
    const rewardType = await RewardType.findOne({
      where: {
        type_key: typeKey,
        is_active: true,
      },
    });

    // 缓存结果（包括null结果，避免缓存穿透）
    if (rewardType) {
      await RedisService.set(cacheKey, JSON.stringify(rewardType.toJSON()), this.CACHE_TTL);
    } else {
      // 缓存空结果，但设置较短的TTL
      await RedisService.set(cacheKey, 'null', 300); // 5分钟
    }

    return rewardType;
  }

  /**
   * 根据ID获取奖励类型（带缓存）
   */
  static async getById(id: number): Promise<RewardType | null> {
    const cacheKey = `${this.CACHE_KEY_PREFIX}id:${id}`;
    
    // 尝试从缓存获取
    const cached = await RedisService.get(cacheKey);
    if (cached) {
      if (cached === 'null') {
        return null;
      }
      try {
        const data = JSON.parse(cached);
        // 重新创建RewardType实例
        return RewardType.build(data);
      } catch (error) {
        console.error('解析缓存数据失败:', error);
        // 缓存数据损坏，删除缓存
        await RedisService.del(cacheKey);
      }
    }

    // 从数据库查询
    const rewardType = await RewardType.findByPk(id);

    // 缓存结果
    if (rewardType) {
      await RedisService.set(cacheKey, JSON.stringify(rewardType.toJSON()), this.CACHE_TTL);
    } else {
      // 缓存空结果，但设置较短的TTL
      await RedisService.set(cacheKey, 'null', 300); // 5分钟
    }

    return rewardType;
  }

  /**
   * 清除指定奖励类型的缓存
   */
  static async clearCache(id: number, typeKey?: string): Promise<void> {
    const keys = [`${this.CACHE_KEY_PREFIX}id:${id}`];
    
    if (typeKey) {
      keys.push(`${this.CACHE_KEY_PREFIX}type_key:${typeKey}`);
    }

    for (const key of keys) {
      await RedisService.del(key);
    }
  }

  /**
   * 预热缓存 - 将所有活跃的奖励类型加载到缓存中
   */
  static async warmupCache(): Promise<void> {
    try {
      const rewardTypes = await RewardType.findAll({
        where: {
          is_active: true,
        },
      });

      const promises = rewardTypes.map(async (rewardType) => {
        const idKey = `${this.CACHE_KEY_PREFIX}id:${rewardType.id}`;
        const typeKeyKey = `${this.CACHE_KEY_PREFIX}type_key:${rewardType.type_key}`;
        const data = JSON.stringify(rewardType.toJSON());

        return Promise.all([
          RedisService.set(idKey, data, this.CACHE_TTL),
          RedisService.set(typeKeyKey, data, this.CACHE_TTL),
        ]);
      });

      await Promise.all(promises);
      console.log(`预热了 ${rewardTypes.length} 个奖励类型的缓存`);
    } catch (error) {
      console.error('预热奖励类型缓存失败:', error);
    }
  }
}
