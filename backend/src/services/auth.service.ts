import { WechatService } from './wechat.service';
import { TokenUtil } from '../utils/token.util';
import { UserService } from './user.service';
import { InvitationService } from './invitation.service';
import { UserRewardService } from './user-reward.service';
import logger from '../utils/logger';

export class AuthService {
  static async loginWithWechat(code: string) {
    // 获取微信用户信息
    const wxResult = await WechatService.code2Session(code);
    
    // 查找或创建用户
    let user = await UserService.getOrCreateUser(wxResult);

    // 生成 token
    const token = TokenUtil.generateToken({
      userId: user.id,
      openid: user.openid
    }); 
    
    return {
      token,
      user: {
        id: user.id,
        username: user.username
      }
    };
  }

}
