import { AppError } from '../middlewares/error.middleware';
import { SuggestedGoal } from '../models';
import PlaySession from '../models/play-session.model';
import sequelize from '../config/sequelize';
import logger from '../utils/logger';

export class GoalService {
  
  /**
   * 批量更新用户选择的目标
   * @param playSessionId 会话ID
   * @param selectedGoalIds 选中的目标ID数组
   */
  static async updateMultipleGoals(playSessionId: number, selectedGoalIds: number[], goalImageKey: string):Promise<SuggestedGoal[]> {
    logger.info(`[GoalService] Updating multiple goals for playSessionId: ${playSessionId}, selectedGoalIds: ${selectedGoalIds}, goalImageKey: ${goalImageKey}`);
    const transaction = await sequelize.transaction();
    
    try{
      // 先查询当前已选中的目标ID
      const currentSelectedGoals = await SuggestedGoal.findAll({
        where: { 
          playSessionId,
          isSelected: true 
        },
        attributes: ['id'],
        transaction
      });
      
      const currentSelectedIds = currentSelectedGoals.map(goal => goal.id).sort();
      const newSelectedIds = [...selectedGoalIds].sort();
      
      // 比较选择是否发生变化
      const hasChanged = currentSelectedIds.length !== newSelectedIds.length || 
                        !currentSelectedIds.every((id, index) => id === newSelectedIds[index]);
      
      // 先将该会话的所有目标设为未选中
      await SuggestedGoal.update(
        { isSelected: false },
        { 
          where: { playSessionId },
          transaction
        }
      );
      
      // 如果有选中的目标，则更新为选中状态
      if (selectedGoalIds.length > 0) {
        await SuggestedGoal.update(
          { isSelected: true, goalImageKey },
          { 
            where: { 
              id: selectedGoalIds,
              playSessionId
            },
            transaction
          }
        );
      }
      
      // 只有在选择发生变化时才清空respWorkflow
      if (hasChanged) {
        await PlaySession.update(
          { respEmpathy: '', respWorkflow: '' },
          { 
            where: { id: playSessionId },
            transaction
          }
        );
      }
      
      // 返回更新后的目标列表
      const result = await SuggestedGoal.findAll({
        where: { playSessionId },
        order: [['id', 'ASC']],
        transaction
      });
      
      await transaction.commit();
      return result;
    }catch(error){
      await transaction.rollback();
      throw new AppError('选择目标失败', 500);
    }
  }
  
  /**
   * 获取会话的所有目标
   * @param playSessionId 会话ID
   */
  static async getGoalsByPlaySession(playSessionId: number) {
    return await SuggestedGoal.findAll({
      where: { playSessionId },
      order: [['id', 'ASC']]
    });
  }
}
