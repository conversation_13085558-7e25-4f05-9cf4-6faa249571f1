import fs from 'fs'
import WxPay from 'wechatpay-node-v3';
import MembershipOrder from '../models/membership-order.model';
import { MembershipPlanService } from './membership-plan.service';
import { UserService } from './user.service';
import { AppError } from '../middlewares/error.middleware';
import logger from '../utils/logger';
import { MembershipPlanKey } from '../enums/membership.enum';
import { UserMembershipOptimizedService } from './user-membership-optimized.service';


/**
 * 会员订单服务
 * 
 * 所需环境变量：
 * - WECHAT_APPID: 微信小程序AppID
 * - WECHAT_MCHID: 微信支付商户号
 * 
 * 所需证书文件（放在项目根目录）：
 * - apiclient_cert.pem: 微信支付API证书公钥
 * - apiclient_key.pem: 微信支付API证书私钥
 */
export class MembershipOrderService {

  static async createOrder(userId: number, planKey: MembershipPlanKey) {
    const baseUrl = process.env.NODE_ENV === 'development' ? 'https://test.moodplay.top' : 'https://moodplay.top';
    try {
      // 1. 获取会员计划信息
      const plan = await MembershipPlanService.getPlanByKey(planKey);
      if (!plan) {
        throw new AppError('会员计划不存在', 404);
      }

      // 2. 获取用户openid
      const openid = await UserService.getUserOpenid(userId);

      // 3. 生成订单号（时间戳+随机数，确保至少6位且唯一）
      const timestamp = Date.now().toString(); // 13位时间戳
      const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0'); // 3位随机数
      const orderNo = timestamp + randomNum; // 16位订单号

      // 4. 创建订单记录
      const order = await MembershipOrder.create({
        orderNo,
        userId,
        planId: plan.id,
        amount: plan.price,
        currency: 'CNY',
        status: 'PENDING'
      });

      // 5. 初始化微信支付
      const certPath = './apiclient_cert.pem';
      const keyPath = './apiclient_key.pem';
      
      // 检查证书文件是否存在
      if (!fs.existsSync(certPath)) {
        throw new AppError(`微信支付证书文件不存在: ${certPath}`, 500);
      }
      if (!fs.existsSync(keyPath)) {
        throw new AppError(`微信支付私钥文件不存在: ${keyPath}`, 500);
      }
      
      const pay = new WxPay({
        appid: process.env.WECHAT_APPID || 'wxcef4c287f6cfbe15',
        mchid: process.env.WECHAT_MCHID || '1713731070',
        publicKey: fs.readFileSync(certPath),
        privateKey: fs.readFileSync(keyPath),
      });
      let totalAmount = 100;
      if(process.env.NODE_ENV === 'production'){
            totalAmount = Math.round(Number(plan.price) * 100)
      } else{
            logger.info(`开发环境，支付金额为1分, 实际金额为 ${Math.round(Number(plan.price) * 100)} 分`);
            totalAmount = 1;
      }
      // 6. 构建微信支付参数
      const params = {
        description: plan.name,
        out_trade_no: order.orderNo, // 使用orderNo作为商户订单号
        notify_url: `${baseUrl}/api/membership-order/notify`, // 
        amount: {
          total: totalAmount, // 微信支付金额单位为分
        },
        payer: {
          openid: openid,
        }
      };

      logger.info(`微信支付下单参数: ${JSON.stringify(params)}`);

      // 7. 调用微信支付下单接口
      const result = await pay.transactions_jsapi(params);
      
      logger.info(`微信支付下单结果: ${JSON.stringify(result)}`);
      if(result.status === 200){
            return result.data;
      }else{
            throw new AppError('创建订单失败', 500);
      }

    } catch (error) {
      logger.error('创建订单失败:', error);
      throw new AppError('创建订单失败', 500);
    }
  }

  /**
   * 处理微信支付回调，更新订单状态
   * @param tradeState 微信支付交易状态
   * @param outTradeNo 商户订单号（我们的orderNo）
   * @param transactionId 微信支付交易号
   * @param successTime 支付成功时间
   */
  static async handlePaymentCallback(
    tradeState: string,
    outTradeNo: string,
    transactionId?: string,
    successTime?: string
  ) {
    try {
      // 1. 根据orderNo查找订单
      const order = await MembershipOrder.findOne({
        where: { orderNo: outTradeNo }
      });

      if (!order) {
        throw new AppError(`订单不存在: ${outTradeNo}`, 404);
      }

      // 2. 映射微信支付状态到我们的订单状态
      let newStatus: 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED' | 'CLOSED';
      let paidAt: Date | null = null;

      switch (tradeState) {
        case 'SUCCESS':
          newStatus = 'PAID';
          paidAt = successTime ? new Date(successTime) : new Date();
          break;
        case 'REFUND':
          newStatus = 'REFUNDED';
          break;
        case 'NOTPAY':
          newStatus = 'PENDING';
          break;
        case 'CLOSED':
          newStatus = 'CLOSED';
          break;
        case 'PAYERROR':
          newStatus = 'FAILED';
          break;
        default:
          logger.warn(`未知的微信支付状态: ${tradeState}`);
          return;
      }

      // 3. 更新订单状态
      await order.update({
        status: newStatus,
        transactionId: transactionId || order.transactionId,
        paidAt: paidAt || order.paidAt,
        paymentMethod: 'WECHAT_PAY'
      });

      logger.info(`订单状态已更新: ${outTradeNo} -> ${newStatus}`);

      // 4. 如果支付成功，激活用户会员
      if (newStatus === 'PAID') {
        await UserMembershipOptimizedService.purchaseMembership(order.userId, order.planId, order.id);
      }

      return order;
    } catch (error) {
      logger.error('处理支付回调失败:', error);
      throw error;
    }
  }

}     