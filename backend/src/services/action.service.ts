import { AppError } from '../middlewares/error.middleware';
import { PlaySession } from '../models';
import { PartnerAiService } from './partner-ai.service';
import { PlaySessionService } from './play-session.service';


export class ActionService {

    static async actions(playSessionId: number) {
      try{
            const playSession = await PlaySessionService.get(playSessionId)
            if(!playSession) {
                  throw new AppError('会话不存在', 404)
            }
            if(!playSession?.inputText) {
                  throw new AppError('会话内容不存在', 404)
            }
            if(!playSession.detectedEmotions || playSession.detectedEmotions.length === 0) {
                  throw new AppError('情绪不存在', 404)
            }
            
            // 根据isSelected分离情绪
            const selectedEmotions = playSession.detectedEmotions
                  .filter(emotion => emotion.isSelected)
                  .map(emotion => emotion.emotionKey);
            const unselectedEmotions = playSession.detectedEmotions
                  .filter(emotion => !emotion.isSelected)
                  .map(emotion => emotion.emotionKey);
            
            // 构建传给AI的内容格式
            const promptContent = `【prompt：${playSession.inputText}。有的情绪：${selectedEmotions.join('；')}。没有的情绪：${unselectedEmotions.join('；')}】`;
            
            const actions = await PartnerAiService.generateActions(promptContent)
            
            // 将 actions 转换为字符串保存
            const actionsString = JSON.stringify(actions.actions);
            await PlaySession.update({ respAction: actionsString }, {
                where: { id: playSessionId }
            });
            
            return actions.actions
      }catch(error){
            throw new AppError('获取行动项失败', 500)
      }
    }
}