import { AppError } from '../middlewares/error.middleware';
import { DivinationInfo } from '../types/divination.type';
import UserDivination from '../models/user-divination.model';
import { OssService } from './oss.service';
import { RedisService } from './redis.service';
import { WechatService } from './wechat.service';
import { PartnerAiService } from './partner-ai.service';
import { Op } from 'sequelize';
import { ZhanbuPayload } from '../types/partner-ai.types';
import logger from '../utils/logger';
import { DivinationConfig } from '../types/config.type';
import { UserService } from './user.service';

export interface CreateDivinationPayload {
  userId: number;
  inputText: string;
  success?: boolean;
  aiResponse: string;
}

export interface DivinationResult {
  success: boolean;
  message: string;
  detail?: string;
  data?: ZhanbuPayload;
  errorType?: 'LIMIT_EXCEEDED' | 'AI_FAILED' | 'SYSTEM_ERROR';
}

export class DivinationService {

    static async getDivinationInfo(userId: number, env: string):Promise<DivinationInfo> {
      try{
            const divinationConfig = await OssService.getObjectJsonContentWithCache<DivinationConfig>('divination');

            divinationConfig.backgroungImage = OssService.generateResourceUrl(divinationConfig.backgroungImage);
            divinationConfig.titleTextImage = OssService.generateResourceUrl(divinationConfig.titleTextImage);
            divinationConfig.guideTextImage = OssService.generateResourceUrl(divinationConfig.guideTextImage);
            divinationConfig.infoIcon = OssService.generateResourceUrl(divinationConfig.infoIcon);
            divinationConfig.loadingIcon = OssService.generateResourceUrl(divinationConfig.loadingIcon);
            divinationConfig.loadingBackgroundImage = OssService.generateResourceUrl(divinationConfig.loadingBackgroundImage);
            divinationConfig.shareBackgroundImage = OssService.generateResourceUrl(divinationConfig.shareBackgroundImage);
            divinationConfig.shareCardBackgroundImage = OssService.generateResourceUrl(divinationConfig.shareCardBackgroundImage);
            divinationConfig.shareHeaderImage = OssService.generateResourceUrl(divinationConfig.shareHeaderImage);
            divinationConfig.shareSubtitle1Image = OssService.generateResourceUrl(divinationConfig.shareSubtitle1Image);
            divinationConfig.shareSubtitle2Image = OssService.generateResourceUrl(divinationConfig.shareSubtitle2Image);
            divinationConfig.shareSubtitle3Image = OssService.generateResourceUrl(divinationConfig.shareSubtitle3Image);
            divinationConfig.mainDarkIcon = OssService.generateResourceUrl(divinationConfig.mainDarkIcon);
            divinationConfig.shareSloganImage = OssService.generateResourceUrl(divinationConfig.shareSloganImage);
            // // 获取小程序码的逻辑
            // const redisKey = `wxacodeDataURL:no_scene`;
            // let wxacodeDataURL = await RedisService.get(redisKey);

            // if (!wxacodeDataURL) {
            //     // 获取小程序码
            //     const wxacodeBuffer = await WechatService.getwxacodeunlimit('no_scene', env)
            //     const wxacodeBase64 = wxacodeBuffer.toString('base64');
            //     wxacodeDataURL = `data:image/png;base64,${wxacodeBase64}`;
            //     // 缓存到 Redis，10分钟
            //     await RedisService.set(redisKey, wxacodeDataURL, 600);
            // }
            const wxacodeBuffer = await WechatService.getwxacodeunlimit('no_scene', env)
            const wxacodeBase64 = wxacodeBuffer.toString('base64');
            const wxacodeDataURL = `data:image/png;base64,${wxacodeBase64}`;
            
            const todayDivinationCount = await DivinationService.getTodayDivinationCount(userId);
            const isReachLimitation = todayDivinationCount >= divinationConfig.limitCount;

            // 
            const userProfile = await UserService.getUserProfile(userId)
            const birthDate = userProfile.birthDate ? new Date(userProfile.birthDate).toISOString().split('T')[0] : '';
            const currentDate = new Date().toISOString().split('T')[0].replace(/-/g, ' / ');
            return {
              config: divinationConfig,
              mpCode: wxacodeDataURL,
              isReachLimitation,
              birthDate,
              currentDate
            }
      }catch(error){
            logger.error(`getDivinationInfo 获取占卜信息失败: ${error}`);
            throw new AppError('获取占卜信息失败', 500)
      }
    }

    /**
     * 创建占卜记录
     * @param payload 占卜数据
     * @returns 创建的占卜记录
     */
    static async createDivinationRecord(payload: CreateDivinationPayload): Promise<UserDivination> {
        try {
            const divination = await UserDivination.create({
                userId: payload.userId,
                inputText: payload.inputText,
                aiResponse: payload.aiResponse,
            });
            return divination;
        } catch (error) {
            logger.error(`创建占卜记录失败: ${error}`);
            throw new AppError('创建占卜记录失败', 500);
        }
    }

    /**
     * 获取用户当天的占卜使用次数
     * @param userId 用户ID
     * @returns 当天占卜次数
     */
    static async getTodayDivinationCount(userId: number): Promise<number> {
        try {
            const today = new Date();
            const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

            const count = await UserDivination.count({
                where: {
                    userId: userId,
                    createdAt: {
                        [Op.gte]: startOfDay,
                        [Op.lt]: endOfDay,
                    },
                },
            });
            return count;
        } catch (error) {
            throw new AppError('获取今日占卜次数失败', 500);
        }
    }

    /**
     * 执行AI占卜
     * @param userId 用户ID
     * @param inputText 占卜问题和生日信息
     * @returns 占卜结果
     */
    static async performDivination(userId: number, inputText: string): Promise<DivinationResult> {
        try {
            // 1. 检查当天占卜次数限制
            const todayCount = await DivinationService.getTodayDivinationCount(userId);
            const divinationConfig = await OssService.getObjectJsonContentWithCache<DivinationConfig>('divination');
            
            if (todayCount >= divinationConfig.limitCount) {
                logger.warn(`用户 ${userId} 已达到当天占卜次数限制: ${todayCount}/${divinationConfig.limitCount}`);
                return {
                    success: false,
                    message: `今日占卜次数已用完，每日限制 ${divinationConfig.limitCount} 次`,
                    errorType: 'LIMIT_EXCEEDED'
                };
            }

            // 2. 调用AI占卜接口
            logger.info(`用户 ${userId} 开始占卜，输入: ${inputText}`);
            const aiResult = await PartnerAiService.generateZhanbu({ text: inputText });
            // 3. 保存占卜记录到数据库
            await DivinationService.createDivinationRecord({
                userId: userId,
                inputText: inputText,
                aiResponse: JSON.stringify(aiResult)
            });
            // 4. 根据AI返回的fulfillment判断结果
            if (aiResult.fulfillment) {
                logger.info(`用户 ${userId} 占卜成功`);
                return {
                    success: true,
                    message: aiResult.reply || '占卜成功',
                    data: aiResult.payload
                };
            } else {
                logger.warn(`用户 ${userId} 占卜失败，AI返回fulfillment=false`);
                return {
                    success: false,
                    message: aiResult.reply || '占卜失败，请稍后再试',
                    detail: aiResult.guard.response,
                    errorType: 'AI_FAILED'
                };
            }

        } catch (error: any) {
            logger.error(`用户 ${userId} 占卜出错:`, error);
            try {
                await DivinationService.createDivinationRecord({
                    userId: userId,
                    inputText: inputText,
                    success: false,
                    aiResponse: JSON.stringify({ error: error.message || '未知错误' })
                });
            } catch (dbError) {
                logger.error('保存占卜错误记录失败:', dbError);
            }

            return {
                success: false,
                message: '占卜服务暂时不可用，请稍后再试',
                errorType: 'SYSTEM_ERROR'
            };
        }
    }
}