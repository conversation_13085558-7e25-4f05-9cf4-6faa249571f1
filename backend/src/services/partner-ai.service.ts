import axios from 'axios';
import axiosRetry from 'axios-retry';
import { AppError } from '../middlewares/error.middleware';

import {
  AnalyzeEmotionResult,
  GenerateActionsResult,
  GenerateQuoteResult,
  SetGoalsResult,
  GenerateEmpathyParams,
  GenerateEmpathyResult,
  ProcessWorkflowParams,
  ProcessWorkflowResponseWrapper,
  GenerateZhanbuParams,
  GenerateZhanbuResult,
  VoiceListResult,
  ProcessWorkflowResult
} from '../types/partner-ai.types';
import logger from '../utils/logger';

const apiClient = axios.create({
  baseURL: process.env.AI_BASE_URL,
  headers: {
    'x-codejoy-api': process.env.AI_API_KEY || 'CJ-uIMFnNqaEREBYxIqcg5d3aj8XeQBxb8gO6S'
  }
});

const apiClientNoRetry = axios.create({
  baseURL: process.env.AI_BASE_URL,
  headers: {
    'x-codejoy-api': process.env.AI_API_KEY || 'CJ-uIMFnNqaEREBYxIqcg5d3aj8XeQBxb8gO6S'
  }
});

// 配置 axios-retry
axiosRetry(apiClient, {
  retries: 1,
  retryDelay: axiosRetry.exponentialDelay,
  retryCondition: (error:any) => {
    const shouldRetry = axiosRetry.isNetworkOrIdempotentRequestError(error) ||
                       (error.response?.status !== undefined && error.response.status >= 500);
    if (shouldRetry) {
        logger.warn(`请求 ${error.config?.url} 失败 (状态码: ${error.response?.status}), 准备重试...`);
    }
    return shouldRetry;
  },
  onRetry: (retryCount:number, error:any, requestConfig:any) => {
    logger.info(`正在对 ${requestConfig.url} 进行第 ${retryCount} 次重试`);
  }
});

export class PartnerAiService {

  static async analyzeEmotion(text: string): Promise<AnalyzeEmotionResult> {
    try {
      const url = `/analyze-emotion`;
      const { data } = await apiClient.post<AnalyzeEmotionResult>(url, {
        text
      });
      return data;
    } catch (error: any) {
      logger.error(`情绪分析错误 (含重试) text: ${text} error:`, error.response?.data || error.message);
      const statusCode = error.response?.status || 500;
      throw new AppError('ooops AI 好像有点问题！', statusCode);
    }
  }
  
  static async setGoals(text: string): Promise<SetGoalsResult> {
    try {
      const url = `/set-goals`;
      logger.info("text: ", text, "url: ", url);
      const { data } = await apiClient.post<SetGoalsResult>(url, {
        text
      });
      return data;
    } catch (error: any) {
      logger.error(`设定目标接口请求失败 (含重试) text: ${text} error:`, error.response?.data || error.message);
      const statusCode = error.response?.status || 500;
      throw new AppError('ooops AI 好像有点问题！', statusCode);
    }
  }
  
  static async generateEmpathy(params: GenerateEmpathyParams): Promise<GenerateEmpathyResult> {
    try {
      logger.info(`generateEmpathy params: ${JSON.stringify(params)}`);
      const url = `/generate-empathy`;

      const { data } = await apiClient.post<GenerateEmpathyResult>(url, params);
      
      // 打印响应结果，排除 audio 字段
      const { audio, ...dataWithoutAudio } = data;
      logger.info(`generateEmpathy response: ${JSON.stringify(dataWithoutAudio)}`);
      
      return data;
    } catch (error: any) {
      logger.error(`generateEmpathy error params: ${JSON.stringify(params)} error:`, error.response?.data || error.message);
      const statusCode = error.response?.status || 500;
      throw new AppError('ooops AI 好像有点问题！', statusCode);
    }
  }

  static async processWorkflow(params: ProcessWorkflowParams): Promise<ProcessWorkflowResult> {
    try {
      logger.info(`processWorkflow params: ${JSON.stringify(params)}`);
      const url = `/generate-workflow`;

      const { data } = await apiClientNoRetry.post<ProcessWorkflowResult>(url, params);
      
      // 打印响应结果，排除 audio 字段
      const { audio, ...dataWithoutAudio } = data;
      logger.info(`processWorkflow response: ${JSON.stringify(dataWithoutAudio)}`);
      
      return data;
    } catch (error: any) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error(`处理工作流程错误 (含重试) params: ${JSON.stringify(params)} error:`, error.response?.data || error.message);
      const errorMessage = error.response?.data?.message || error.response?.data?.error || error.message || '未知错误';
      const statusCode = error.response?.status || 500;
      logger.error("处理工作流程错误详情:", errorMessage, statusCode);
      throw new AppError('ooops AI 好像有点问题！', statusCode);
    }
  }
  

  static async generateQuote(text: string):Promise<GenerateQuoteResult>{
    try {
      const url = `/generate-quote`;
      const { data } = await apiClient.post<GenerateQuoteResult>(url, {
        text
      });
      return data;
    } catch (error: any) {
      logger.error("生成金句错误 (含重试):", error.response?.data || error.message);
      const statusCode = error.response?.status || 500;
      throw new AppError('ooops AI 好像有点问题！', statusCode);
    }
  }

  static async generateActions(text: string):Promise<GenerateActionsResult>{
    try {
      const url = `/generate-actions`;
      logger.info(`generateActions text: ${text}`);
      const { data } = await apiClient.post<GenerateActionsResult>(url, {
        text
      });
      return data;
    } catch (error: any) {
      logger.error(`生成行动项错误 (含重试) text: ${text} error:`, error.response?.data || error.message);
      const statusCode = error.response?.status || 500;
      throw new AppError('ooops AI 好像有点问题！', statusCode);
    }
  }

  static async generateZhanbu(params: GenerateZhanbuParams): Promise<GenerateZhanbuResult> {
    try {
      const url = `/generate-zhanbu`;
      logger.info(`generateZhanbu requestBody: ${JSON.stringify(params)}`);
      const { data } = await apiClient.post<GenerateZhanbuResult>(url, params);
      logger.info(`generateZhanbu response: ${JSON.stringify(data)}`);
      return data;
    } catch (error: any) {
      logger.error(`生成占卜错误 (含重试) params: ${JSON.stringify(params)} error:`, error.response?.data || error.message);
      throw error;
    }
  }

  static async getVoiceList(): Promise<VoiceListResult> {
    try {
      const url = `/api/voice`;
      logger.info('获取语音列表...');
      const { data } = await apiClient.get<VoiceListResult>(url);
      logger.info(`获取语音列表成功，共 ${data.length} 个语音`);
      return data;
    } catch (error: any) {
      logger.error('获取语音列表错误 (含重试):', error.response?.data || error.message);
      const statusCode = error.response?.status || 500;
      throw new AppError('ooops AI 好像有点问题！', statusCode);
    }
  }
}