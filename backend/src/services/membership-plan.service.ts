import MembershipPlan from '../models/membership-plan.model';
import { MembershipPlanKey } from '../enums/membership.enum';

export class MembershipPlanService {
  /**
   * 通过 planKey 获取会员计划信息
   * @param planKey MembershipPlanKey 枚举值
   * @returns 对应的 MembershipPlan 实例或 null
   */
  static async getPlanByKey(planKey: MembershipPlanKey) {
    return MembershipPlan.findOne({ where: { planKey } });
  }

  static async getPlanById(planId: number) {
    return MembershipPlan.findByPk(planId);
  }

  static async getPlanMap() {
    const plans = await MembershipPlan.findAll();
    return plans.reduce((acc, plan) => {
      acc[plan.planKey] = plan;
      return acc;
    }, {} as Record<MembershipPlanKey, MembershipPlan>);
  }
}
