import { RedisService } from './redis.service';
import logger from '../utils/logger';

export interface SystemStatus {
  isAvailable: boolean;
  message: string;
}

export class SystemStatusService {
  private static readonly REDIS_KEY = 'system:status';
  private static readonly DEFAULT_MESSAGE = '服务当前不可用，请稍后再试';

  /**
   * 获取系统状态
   * 如果Redis不可用或键不存在，默认返回系统可用
   */
  static async getSystemStatus(): Promise<SystemStatus> {
    try {
      const statusData = await RedisService.get(this.REDIS_KEY);
      
      if (!statusData) {
        // Redis中没有数据，默认系统可用
        logger.debug('系统状态键不存在，默认返回可用状态');
        return {
          isAvailable: true,
          message: ''
        };
      }

      const parsedStatus = JSON.parse(statusData);
      return {
        isAvailable: parsedStatus.isAvailable ?? true,
        message: parsedStatus.message || this.DEFAULT_MESSAGE
      };
    } catch (error) {
      // Redis连接失败或解析错误，默认返回系统可用
      logger.warn('获取系统状态失败，默认返回可用状态:', error);
      return {
        isAvailable: true,
        message: ''
      };
    }
  }

  /**
   * 设置系统状态
   */
  static async setSystemStatus(isAvailable: boolean, message?: string): Promise<boolean> {
    try {
      const statusData = {
        isAvailable,
        message: message || this.DEFAULT_MESSAGE
      };

      const success = await RedisService.set(this.REDIS_KEY, JSON.stringify(statusData));
      
      if (success) {
        logger.info(`系统状态已更新: ${isAvailable ? '可用' : '不可用'}, 消息: ${statusData.message}`);
      } else {
        logger.error('更新系统状态失败');
      }
      
      return success;
    } catch (error) {
      logger.error('设置系统状态时发生错误:', error);
      return false;
    }
  }

  /**
   * 获取系统可用性（简化版本，只返回布尔值）
   */
  static async isSystemAvailable(): Promise<boolean> {
    const status = await this.getSystemStatus();
    return status.isAvailable;
  }

  /**
   * 设置系统不可用
   */
  static async setSystemUnavailable(message?: string): Promise<boolean> {
    return this.setSystemStatus(false, message);
  }

  /**
   * 设置系统可用
   */
  static async setSystemAvailable(): Promise<boolean> {
    return this.setSystemStatus(true, '');
  }
}
