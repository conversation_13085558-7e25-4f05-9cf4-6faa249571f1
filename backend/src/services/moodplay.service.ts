import { AppError } from '../middlewares/error.middleware';
import { OssService } from './oss.service';
import { MoodplayEntryResult } from '../types/moodplay.type';

import { PlaySessionService } from './play-session.service';
import logger from '../utils/logger';

export class MoodplayService {

    static async entry(playSessionId: number):Promise<MoodplayEntryResult>{
        try {
            const playSession = await PlaySessionService.get(playSessionId)
            if (!playSession) {
                throw new AppError('playSessionId不存在', 404);
            }
            const selectedGoal = playSession.suggestedGoals?.filter(p => p.isSelected === true)[0]
            if(!selectedGoal){
                throw new AppError('没有找到目标', 404);
            }
            const assets = await OssService.getPageAssets('moodplay-page');

            return {
                endSoundEffects: assets['endSoundEffects'],
                seamSoundEffects: assets['seamSoundEffects'],
                goalName: selectedGoal.goalKey,
                goalDescription: selectedGoal.goalDescription || '',
                experienceMode: playSession.experienceMode
            }
        } catch (error) {
            logger.error("get config data error: ", error);
            throw new AppError('获取moodplay配置数据失败', 500);
        }
    }
}