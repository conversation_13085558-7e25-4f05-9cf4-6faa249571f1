import { AppError } from '../middlewares/error.middleware';
import { PlaySession, SuggestedGoal } from '../models';
import { CommonConfig, EndGoalConfig, QuoteConfig } from '../types/config.type';
import { QuoteInfo } from '../types/quote.type';
import logger from '../utils/logger';
import { OssService } from './oss.service';
import { PartnerAiService } from './partner-ai.service';
import { WechatService } from './wechat.service';
import { RedisService } from './redis.service';
import { GenerateQuoteResult, Quote, QuoteResponse } from '../types/partner-ai.types';

export class QuoteService {

    /**
     * 从AI返回的quotes中选择一个未重复的quote
     * @param userId 用户ID
     * @param quotes AI返回的quotes数组(固定3个元素)
     * @returns 选中的quote对象和其索引
     */
    private static async selectNonDuplicateQuote(userId: number, quotes: Quote[]): Promise<{ selectedQuote: Quote; selectedIndex: number }> {
        const redisKey = `user_recent_quotes:${userId}`;
        
        try {
            // 获取用户最近3次的quotes
            const recentQuotes = await RedisService.lrange(redisKey, 0, 2); // 获取最近3个
            
            // 按顺序检查quotes[0], quotes[1], quotes[2]
            for (let i = 0; i < quotes.length; i++) {
                const currentQuote = quotes[i].quote;
                // 检查当前quote是否在最近3次中出现过
                if (!recentQuotes.includes(currentQuote)) {
                    // 未重复，选择此quote并更新记录
                    await this.updateRecentQuotes(userId, currentQuote);
                    logger.info(`Selected quote[${i}] for user ${userId}: ${currentQuote}`);
                    return { selectedQuote: quotes[i], selectedIndex: i };
                }
            }
            
            // 如果都重复了，使用quotes[0]并更新记录
            await this.updateRecentQuotes(userId, quotes[0].quote);
            logger.info(`All quotes duplicated for user ${userId}, using quote[0]: ${quotes[0].quote}`);
            return { selectedQuote: quotes[0], selectedIndex: 0 };
            
        } catch (error) {
            logger.error('Error in selectNonDuplicateQuote:', error);
            // 出错时返回第一个quote
            return { selectedQuote: quotes[0], selectedIndex: 0 };
        }
    }

    /**
     * 更新用户最近的quotes记录
     * @param userId 用户ID  
     * @param quote 新的quote
     */
    private static async updateRecentQuotes(userId: number, quote: string): Promise<void> {
        const redisKey = `user_recent_quotes:${userId}`;
        
        try {
            // 将新quote添加到列表头部
            await RedisService.lpush(redisKey, quote);
            // 保持列表只有3个元素
            await RedisService.ltrim(redisKey, 0, 2);
            // 设置30天过期时间
            await RedisService.expire(redisKey, 30 * 24 * 60 * 60);
        } catch (error) {
            logger.error('Error updating recent quotes:', error);
        }
    }

    /**
     * 提前异步生成并存储 quote
     * @param playSessionId 播放会话ID
     */
    static async preGenerateQuote(playSessionId: number): Promise<void> {
        try {
            logger.info(`preGenerateQuote: ${playSessionId}`);
            
            // 获取会话信息
            const playSession = await PlaySession.findByPk(playSessionId);
            if (!playSession || !playSession.inputText) {
                logger.warn(`PlaySession ${playSessionId} not found or has no input text`);
                return;
            }
            
            // 检查是否已经生成过 quote
            if (playSession.respQuote) {
                logger.info(`Quote already exists for session ${playSessionId}`);
                return;
            }
            
            // 生成 quote
            const quoteResult = await PartnerAiService.generateQuote(playSession.inputText);
            const { selectedQuote, selectedIndex } = await this.selectNonDuplicateQuote(playSession.userId, quoteResult.quotes);
            
            // 构造完整的quote响应
            const quoteResponse: QuoteResponse = {
                selectedQuote,
                originalQuoteResult: quoteResult,
                selectedIndex,
                generatedAt: new Date().toISOString()
            };
            
            // 存储到数据库
            await PlaySession.update(
                { respQuote: JSON.stringify(quoteResponse) },
                { where: { id: playSessionId } }
            );
            
            logger.info(`Quote pre-generated and stored for session ${playSessionId}`);
        } catch (error) {
            logger.error(`Failed to pre-generate quote for session ${playSessionId}:`, error);
            // 不抛出错误，因为这是异步操作，不应该影响主流程
        }
    }

    static async getQuoteInfo(userId: number, playSessionId: number, env: string): Promise<QuoteInfo> {
      try{
            logger.info(`getQuoteInfo userId: ${userId}, playSessionId: ${playSessionId}`)
            
            // 第一步：必须先验证PlaySession存在
            const playSession = await PlaySession.findByPk(playSessionId)
            if(!playSession) {
                  throw new AppError('会话不存在', 404)
            }
            if(!playSession?.inputText) {
                  throw new AppError('会话内容不存在', 404)
            }
            if (playSession.userId !== userId) {
                throw new AppError('Play session not found or access denied', 404);
            }

            // 第二步：检查是否已有预生成的 quote
            let selectedQuote: Quote | null = null;
            let originalQuoteResult: GenerateQuoteResult | null = null;
            let needGenerateQuote = false;
            
            if (playSession.respQuote) {
                try {
                    const parsedQuote = JSON.parse(playSession.respQuote);
                    
                    // 检查是否为新格式（QuoteResponse）
                    if (parsedQuote.selectedQuote && parsedQuote.originalQuoteResult) {
                        // 新格式：包含完整的quote响应信息
                        selectedQuote = parsedQuote.selectedQuote;
                        originalQuoteResult = parsedQuote.originalQuoteResult;
                        logger.info(`Using pre-generated quote (new format) for session ${playSessionId}`);
                    } else if (parsedQuote.quote && parsedQuote.author) {
                        // 旧格式：只包含selectedQuote
                        selectedQuote = parsedQuote;
                        logger.info(`Using pre-generated quote (old format) for session ${playSessionId}`);
                    } else {
                        logger.warn(`Invalid quote format, will regenerate`);
                        needGenerateQuote = true;
                    }
                } catch (error) {
                    logger.warn(`Failed to parse existing quote, will regenerate: ${error}`);
                    needGenerateQuote = true;
                }
            } else {
                needGenerateQuote = true;
            }

            // 第三步：并行执行所有不相互依赖的操作
            const [
                wxacodeBuffer,
                quoteConfig, 
                commonConfig, 
                endGoalConfig,
                selectedGoals,
                quoteResult
            ] = await Promise.all([
                WechatService.getwxacodeunlimit('no_scene', env),
                OssService.getObjectJsonContentWithCache<QuoteConfig>('quote'),
                OssService.getObjectJsonContentWithCache<CommonConfig>('common'),
                OssService.getObjectJsonContentWithCache<EndGoalConfig>('end-goal'),
                SuggestedGoal.findAll({
                    where: { playSessionId: playSessionId, isSelected: true }
                }),
                needGenerateQuote ? PartnerAiService.generateQuote(playSession.inputText) : Promise.resolve(null)
            ]);

            // 验证选中的目标
            if (!selectedGoals || selectedGoals.length === 0) {
                throw new AppError('No selected goal found for this session. Please select a goal first.', 400);
            }

            // 第四步：如果需要生成 quote，处理生成结果
            if (needGenerateQuote && quoteResult) {
                logger.info(`quoteResult: ${JSON.stringify(quoteResult)}`);
                const { selectedQuote: newSelectedQuote, selectedIndex } = await this.selectNonDuplicateQuote(userId, quoteResult.quotes);
                selectedQuote = newSelectedQuote;
                originalQuoteResult = quoteResult;
                
                // 构造完整的quote响应
                const quoteResponse: QuoteResponse = {
                    selectedQuote: newSelectedQuote,
                    originalQuoteResult: quoteResult,
                    selectedIndex,
                    generatedAt: new Date().toISOString()
                };
                
                // 异步更新数据库，不阻塞响应
                PlaySession.update({ respQuote: JSON.stringify(quoteResponse) }, {
                    where: { id: playSessionId }
                }).catch(error => {
                    logger.error(`Failed to update quote in database: ${error}`);
                });
            }

            // 确保 selectedQuote 不为空
            if (!selectedQuote) {
                throw new AppError('Failed to get quote', 500);
            }

            // 第五步：处理其他依赖操作
            const wxacodeBase64 = wxacodeBuffer.toString('base64');
            
            // 第六步：并行执行更新操作和其他处理
            const primaryGoal = selectedGoals[0];
            const theGoalConfig = endGoalConfig.goalConfigs.filter(goalConfig => goalConfig.goalKey === primaryGoal.goalKey)[0];
            
            if(!theGoalConfig){
                logger.error(`theGoalConfig not found`)
                throw new AppError('Goal config not found', 400);
            }
            logger.info(`theGoalConfig: ${JSON.stringify(theGoalConfig)}`)
            
            if(!primaryGoal.goalImageKey){
                throw new AppError('primaryGoal goalImageKey为空', 500);
            }

            // 第七步：并行执行最后的操作
            const backgroundImage = OssService.generateResourceUrl(primaryGoal.goalImageKey);
            const mainIcon = OssService.generateResourceUrl(commonConfig.mainIcon);
            const saveButtonIcon = OssService.generateResourceUrl(quoteConfig.saveButtonIcon);
            const glitchImage = OssService.generateResourceUrl(quoteConfig.glitchImage);
            const centerMaskImage = OssService.generateResourceUrl(quoteConfig.centerMaskImage);
            const modalButtonAreaImage = OssService.generateResourceUrl(quoteConfig.modalButtonAreaImage);
            const wxacodeDataURL = `data:image/png;base64,${wxacodeBase64}`;
            
            // 构造返回数据
            const result: QuoteInfo = {
                  quote: selectedQuote.quote,
                  author: selectedQuote.author,
                  backgroundImage,
                  mainIcon,
                  mainColor: theGoalConfig.mainColor,
                  buttonText: quoteConfig.buttonText,
                  saveButtonIcon,
                  modalTitle: quoteConfig.modalTitle,
                  modalSaveButtonText: quoteConfig.modalSaveButtonText,
                  modalShareButtonText: quoteConfig.modalShareButtonText,
                  modalCanvasTitle: quoteConfig.modalCanvasTitle,
                  modalCanvasSlogan: quoteConfig.modalCanvasSlogan,
                  modalButtonAreaImage: modalButtonAreaImage,
                  glitchImage: glitchImage,
                  centerMaskImage: centerMaskImage,
                  modalMpCode: wxacodeDataURL
            };
            
            // 只有当存在原始quote结果时才添加额外字段
            if (originalQuoteResult) {
                result.originalQuoteResult = originalQuoteResult;
                
                // 尝试从存储的数据中获取selectedIndex
                if (playSession.respQuote) {
                    try {
                        const parsedQuote = JSON.parse(playSession.respQuote);
                        if (parsedQuote.selectedIndex !== undefined) {
                            result.selectedIndex = parsedQuote.selectedIndex;
                        }
                    } catch (error) {
                        logger.warn(`Failed to parse selectedIndex from stored quote: ${error}`);
                    }
                }
            }
            
            return result;
      }catch(error){
            logger.error(`getQuote error`, error)
            throw new AppError('get quote failed', 500)
      }
    }

}