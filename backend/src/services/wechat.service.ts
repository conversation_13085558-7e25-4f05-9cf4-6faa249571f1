import axios from 'axios';
import { WechatCode2SessionResult } from '../types/wechat.type';
import { wechatConfig } from '../config/wechat';
import { AppError } from '../middlewares/error.middleware';
import { RedisService } from './redis.service';
import FormData from 'form-data';
import logger from '../utils/logger';
import { Logger } from 'winston';
import crypto from 'crypto';

interface AccessTokenResult {
  access_token: string;
  expires_in: number;
}

interface VoiceRecognitionResult {
  errcode?: number;
  errmsg?: string;
  result?: string;
}

export interface WechatPhoneInfo {
  phoneNumber: string;
  purePhoneNumber: string;
  countryCode: string;
  watermark?: {
    timestamp: number;
    appid: string;
  };
}

export class WechatService {

  private static readonly ACCESS_TOKEN_KEY = 'wechat:access_token';

  static async getwxacodeunlimit(scene: string, env:string): Promise<Buffer> {
    const accessToken = await this.getAccessToken();
    const url = `https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=${accessToken}`;
    
    const data = {
      "page": "pages/index/index",
      "scene": scene,
      "check_path": env === 'release' ? true : false,
      "env_version": env
    }
    logger.info("getwxacodeunlimit data ", data)
    try {
      const response = await axios.post(url, data, {
          responseType: 'arraybuffer'
      });

      if (response.headers['content-type'] && response.headers['content-type'].includes('application/json')) {
        const errorData = JSON.parse(Buffer.from(response.data).toString('utf8'));
        logger.error('获取小程序码失败(微信返回JSON):', errorData);
        throw new AppError(`获取小程序码失败: ${errorData.errmsg || '未知微信API错误'}`, 500);
      }
      
      return Buffer.from(response.data);
    } catch (error) {
      logger.error('获取小程序码请求处理失败:', error);
      if (error instanceof AppError) throw error;
      throw new AppError('获取小程序码请求失败', 500);
    }
  }

  static async getAccessToken(): Promise<string> {
    try {
      // 1. 先从Redis获取
      const cachedToken = await RedisService.get(this.ACCESS_TOKEN_KEY);
      if (cachedToken) {
        return cachedToken;
      }

      // 2. Redis没有则从微信服务器获取
      const url = 'https://api.weixin.qq.com/cgi-bin/token';
      const { data } = await axios.get<AccessTokenResult>(url, {
        params: {
          grant_type: 'client_credential',
          appid: wechatConfig.appId,
          secret: wechatConfig.appSecret
        }
      });

      if (!data.access_token) {
        throw new AppError('获取access_token失败', 500);
      }

      // 3. 存入Redis，设置过期时间为7000秒(比微信返回的7200秒少一点，确保安全)
      await RedisService.set(this.ACCESS_TOKEN_KEY, data.access_token, 5400);

      return data.access_token;
    } catch (error) {
      logger.error('获取access_token错误:', error);
      throw new AppError('获取access_token失败', 500);
    }
  }

  static async code2Session(code: string): Promise<WechatCode2SessionResult> {
    try {
      const url = 'https://api.weixin.qq.com/sns/jscode2session';
      const { data } = await axios.get(url, {
        params: {
          appid: wechatConfig.appId,
          secret: wechatConfig.appSecret,
          js_code: code,
          grant_type: 'authorization_code'
        }
      });

      if (data.errcode && data.errcode !== 0) {
        throw new AppError(`微信登录失败: ${data.errmsg}`, 400);
      }

      return data;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error(error);
      throw new AppError('微信服务器请求失败', 500);
    }
  }

  /**
   * 提交语音进行识别
   * @param voiceBuffer 语音文件的Buffer
   * @param voiceId 语音唯一标识
   * @param format 文件格式(mp3)
   * @param lang 语言(zh_CN/en_US)
   */
  static async submitVoiceForRecognition(
    voiceBuffer: Buffer,
    voiceId: string,
    format: string = 'mp3',
    lang: string = 'zh_CN'
  ): Promise<void> {
    try {
      const accessToken = await this.getAccessToken();
      const url = `https://api.weixin.qq.com/cgi-bin/media/voice/addvoicetorecofortext?access_token=${accessToken}`;
      
      // 创建FormData对象
      const formData = new FormData();
      formData.append('voice', voiceBuffer, {
        filename: `${voiceId}.${format}`,
        contentType: 'audio/mpeg'
      });

      const response = await axios.post(url, formData, {
        params: {
          format,
          voice_id: voiceId,
          lang
        },
        headers: {
          ...formData.getHeaders(),
          'Content-Length': voiceBuffer.length
        }
      });

      logger.info("response.data", response.data)

      if (response.data.errcode && response.data.errcode !== 0) {
        throw new AppError(`提交语音识别失败: ${response.data.errmsg}`, 400);
      }
    } catch (error) {
      logger.error('提交语音识别错误:', error);
      if (error instanceof AppError) throw error;
      throw new AppError('提交语音识别失败', 500);
    }
  }

  /**
   * 查询语音识别结果
   * @param voiceId 语音唯一标识
   * @param lang 语言(zh_CN/en_US)
   * @returns 识别结果文本
   */
  static async queryVoiceRecognitionResult(
    voiceId: string,
    lang: string = 'zh_CN'
  ): Promise<string> {
    try {
      const accessToken = await this.getAccessToken();
      const url = `https://api.weixin.qq.com/cgi-bin/media/voice/queryrecoresultfortext`;
      
      const response = await axios.post<VoiceRecognitionResult>(
        url,
        {},
        {
          params: {
            access_token: accessToken,
            voice_id: voiceId,
            lang
          }
        }
      );

      if (response.data.errcode && response.data.errcode !== 0) {
        throw new AppError(`查询语音识别结果失败: ${response.data.errmsg}`, 400);
      }

      if (!response.data.result) {
        // throw new AppError('未获取到识别结果', 404);
        logger.warn('未获取到识别结果')
        return ""
      }
      return response.data.result;
    } catch (error) {
      logger.error('查询语音识别结果错误:', error);
      if (error instanceof AppError) throw error;
      throw new AppError('查询语音识别结果失败', 500);
    }
  }

  static async getPhoneNumberViaCode(apiCode: string): Promise<WechatPhoneInfo> {
    const accessToken = await this.getAccessToken();
    const url = `https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=${accessToken}`;

    try {
      const response = await axios.post<{
        errcode: number;
        errmsg: string;
        phone_info?: WechatPhoneInfo;
      }>(url, { code: apiCode });

      const { data } = response;
      logger.info('WechatService.getPhoneNumberViaCode response data:', data);

      if (data.errcode !== 0 || !data.phone_info) {
        logger.error(`获取手机号失败(微信API): ${data.errmsg || '未知错误'}`, data);
        throw new AppError(`获取手机号失败: ${data.errmsg || '微信API返回错误'}`, data.errcode || 400);
      }
      return data.phone_info;
    } catch (error) {
      logger.error('getPhoneNumberViaCode 请求处理失败:', error);
      if (error instanceof AppError) throw error;
      if (axios.isAxiosError(error) && error.response && error.response.data) {
        const errData = error.response.data;
        logger.error('Axios error data from WeChat getPhoneNumber API:', errData);
        throw new AppError(`请求微信获取手机号接口失败: ${errData.errmsg || '微信API错误'}`, errData.errcode || 500);
      }
      throw new AppError('请求微信获取手机号接口失败', 500);
    }
  }

  static decryptData(encryptedData: string, iv: string, sessionKey: string): any {
    try {
      const sessionKeyBuffer = Buffer.from(sessionKey, 'base64');
      const ivBuffer = Buffer.from(iv, 'base64');
      const encryptedDataBuffer = Buffer.from(encryptedData, 'base64');

      const decipher = crypto.createDecipheriv('aes-128-cbc', sessionKeyBuffer, ivBuffer);
      decipher.setAutoPadding(true); 

      let decoded = decipher.update(encryptedDataBuffer, undefined, 'utf8');
      decoded += decipher.final('utf8');
      
      const decodedJSON = JSON.parse(decoded);
      
      // Optional: Validate appid if watermark is present and necessary
      // if (decodedJSON.watermark && decodedJSON.watermark.appid !== wechatConfig.appId) {
      //   logger.error('Decrypted data appid mismatch', { expected: wechatConfig.appId, actual: decodedJSON.watermark.appid });
      //   throw new AppError('Decrypted data appid mismatch', 400);
      // }

      return decodedJSON;
    } catch (error) {
      logger.error('解密微信数据失败:', error);
      throw new AppError('解密数据失败', 500);
    }
  }
}