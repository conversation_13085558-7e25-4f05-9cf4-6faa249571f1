import Redis from 'ioredis';
import logger from '../utils/logger';

export class RedisService {
    private static instance: Redis.Redis | null = null;

    private constructor() { }

    public static getInstance(): Redis.Redis {
        if (!RedisService.instance) {
            RedisService.instance = new Redis({
                host: process.env.REDIS_HOST || 'localhost',
                port: Number(process.env.REDIS_PORT) || 6379,
                connectTimeout: 3000, // 5秒连接超时
                commandTimeout: 1500, // 3秒命令超时
                maxRetriesPerRequest: 1, // 最多重试1次
                lazyConnect: true, // 延迟连接
            });
        }
        return RedisService.instance;
    }

    public static async closeConnection(): Promise<void> {
        if (RedisService.instance) {
            await RedisService.instance.quit();
            RedisService.instance = null;
        }
    }

    /**
     * 设置带 NX 条件的键值对（仅当键不存在时设置）
     * @param key 键名
     * @param value 值
     * @param expireInSeconds 过期时间（秒）
     * @returns 是否设置成功
     */
    public static async setNX(key: string, value: string, expireInSeconds: number): Promise<boolean> {
        const client = RedisService.getInstance();
        const result = await client.set(key, value, 'EX', expireInSeconds, 'NX');
        return result === 'OK';
    }

    /**
     * 安全的设置操作，如果设置失败会返回 false
     */
    public static async set(key: string, value: string, expireInSeconds?: number): Promise<boolean> {
        try {
            const client = RedisService.getInstance();
            if (expireInSeconds) {
                await client.set(key, value, 'EX', expireInSeconds);
                logger.debug(`Redis SET success: ${key} with TTL ${expireInSeconds}s`);
            } else {
                await client.set(key, value);
                logger.debug(`Redis SET success: ${key}`);
            }
            return true;
        } catch (error) {
            logger.error(`Redis SET failed for key: ${key}, TTL: ${expireInSeconds}s`, error);
            return false;
        }
    }

    public static async get(key: string): Promise<string | null> {
        const client = RedisService.getInstance();
        return await client.get(key);
    }

    /**
     * 获取整数值
     */
    public static async getInt(key: string): Promise<number | null> {
        const value = await this.get(key);
        if (value === null) return null;
        const parsed = parseInt(value, 10);
        return isNaN(parsed) ? null : parsed;
    }

    public static async del(key: string): Promise<number> {
        const client = RedisService.getInstance();
        return await client.del(key);
    }

    /**
     * 获取列表指定范围的元素
     * @param key 列表键名
     * @param start 开始索引
     * @param stop 结束索引
     * @returns 列表元素数组
     */
    public static async lrange(key: string, start: number, stop: number): Promise<string[]> {
        const client = RedisService.getInstance();
        return await client.lrange(key, start, stop);
    }

    /**
     * 在列表头部插入元素
     * @param key 列表键名
     * @param value 要插入的值
     * @returns 插入后列表的长度
     */
    public static async lpush(key: string, value: string): Promise<number> {
        const client = RedisService.getInstance();
        return await client.lpush(key, value);
    }

    /**
     * 修剪列表，只保留指定范围内的元素
     * @param key 列表键名
     * @param start 开始索引
     * @param stop 结束索引
     * @returns 操作结果
     */
    public static async ltrim(key: string, start: number, stop: number): Promise<string> {
        const client = RedisService.getInstance();
        return await client.ltrim(key, start, stop);
    }

    /**
     * 设置键的过期时间
     * @param key 键名
     * @param seconds 过期时间（秒）
     * @returns 是否设置成功
     */
    public static async expire(key: string, seconds: number): Promise<number> {
        const client = RedisService.getInstance();
        return await client.expire(key, seconds);
    }
}