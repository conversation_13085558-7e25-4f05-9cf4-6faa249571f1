import sharp from 'sharp';
import crypto from 'crypto';
import axios from 'axios';
import { OssService } from './oss.service';
import logger from '../utils/logger';
import { TapTalkConfig } from '../types/config.type';
import { TapTalk, TapTalkItem } from '../types/tap-talk.type';

export class ImageProcessingService {
  private static processedImageCache: Map<string, string> = new Map();
  private static processingQueue: Set<string> = new Set();
  
  /**
   * 为图片添加模糊效果
   * @param imageUrl 原图片URL
   * @param blurRadius 模糊半径，默认为7
   * @param overlayColor 叠加颜色，默认为rgba(0,0,0,0.15)
   * @returns 处理后的图片URL
   */
  static async addBlurEffect(
    imageUrl: string, 
    blurRadius: number = 7,
    overlayColor: { r: number, g: number, b: number, alpha: number } = { r: 0, g: 0, b: 0, alpha: 0.15 }
  ): Promise<string> {
    try {
      // 生成缓存key
      const cacheKey = this.generateCacheKey(imageUrl, blurRadius, overlayColor);
      
      // 检查缓存
      if (this.processedImageCache.has(cacheKey)) {
        return this.processedImageCache.get(cacheKey)!;
      }
      
      // 从imageUrl提取OSS对象名称
      const objectName = OssService.extractObjectNameFromUrl(imageUrl);
      if (!objectName) {
        logger.error(`无法从URL提取对象名称: ${imageUrl}`);
        return imageUrl; // 返回原图
      }
      
      // 生成处理后的文件名
      const processedObjectName = this.generateProcessedObjectName(objectName, blurRadius, overlayColor);
      
      // 检查处理后的图片是否已经存在
      const processedImageExists = await OssService.objectExists(processedObjectName);
      if (processedImageExists) {
        const processedUrl = OssService.generateResourceUrl(processedObjectName);
        this.processedImageCache.set(cacheKey, processedUrl);
        return processedUrl;
      }
      
      // 下载原图
      const originalImageBuffer = await this.downloadImage(imageUrl);
      
      // 处理图片
      const processedImageBuffer = await this.processImage(originalImageBuffer, blurRadius, overlayColor);
      
      // 上传处理后的图片
      await OssService.putObjectBuffer(processedObjectName, processedImageBuffer, {
        contentType: 'image/jpeg'
      });
      
      // 生成处理后的图片URL
      const processedUrl = OssService.generateResourceUrl(processedObjectName);
      
      // 缓存URL
      this.processedImageCache.set(cacheKey, processedUrl);
      
      return processedUrl;
      
    } catch (error) {
      logger.error(`图片处理失败: ${imageUrl}`, error);
      return imageUrl; // 处理失败时返回原图
    }
  }
  
  /**
   * 异步处理图片（不阻塞主线程）
   * @param imageUrl 原图片URL
   * @param blurRadius 模糊半径，默认为7
   * @param overlayColor 叠加颜色，默认为rgba(0,0,0,0.15)
   */
  static async addBlurEffectAsync(
    imageUrl: string, 
    blurRadius: number = 7,
    overlayColor: { r: number, g: number, b: number, alpha: number } = { r: 0, g: 0, b: 0, alpha: 0.15 }
  ): Promise<void> {
    const cacheKey = this.generateCacheKey(imageUrl, blurRadius, overlayColor);
    
    // 如果已在处理队列中，则跳过
    if (this.processingQueue.has(cacheKey)) {
      return;
    }
    
    // 如果已经处理过，则跳过
    if (this.processedImageCache.has(cacheKey)) {
      return;
    }
    
    // 添加到处理队列
    this.processingQueue.add(cacheKey);
    
    // 异步处理，不阻塞主线程
    setImmediate(async () => {
      try {
        logger.info(`[异步处理] 开始处理图片: ${imageUrl}`);
        await this.addBlurEffect(imageUrl, blurRadius, overlayColor);
        logger.info(`[异步处理] 图片处理完成: ${imageUrl}`);
      } catch (error) {
        logger.error(`[异步处理] 图片处理失败: ${imageUrl}`, error);
      } finally {
        // 从处理队列中移除
        this.processingQueue.delete(cacheKey);
      }
    });
  }
  
  /**
   * 检查处理后的图片是否存在
   * @param imageUrl 原图片URL
   * @param blurRadius 模糊半径，默认为7
   * @param overlayColor 叠加颜色，默认为rgba(0,0,0,0.15)
   * @returns 处理后的图片URL或null
   */
  static async getProcessedImageUrl(
    imageUrl: string, 
    blurRadius: number = 7,
    overlayColor: { r: number, g: number, b: number, alpha: number } = { r: 0, g: 0, b: 0, alpha: 0.15 }
  ): Promise<string | null> {
    try {
      const cacheKey = this.generateCacheKey(imageUrl, blurRadius, overlayColor);
      
      // 检查内存缓存
      if (this.processedImageCache.has(cacheKey)) {
        return this.processedImageCache.get(cacheKey)!;
      }
      
      // 从imageUrl提取OSS对象名称
      const objectName = OssService.extractObjectNameFromUrl(imageUrl);
      if (!objectName) {
        return null;
      }
      
      // 生成处理后的文件名
      const processedObjectName = this.generateProcessedObjectName(objectName, blurRadius, overlayColor);
      
      // 检查处理后的图片是否已经存在
      const processedImageExists = await OssService.objectExists(processedObjectName);
      if (processedImageExists) {
        const processedUrl = OssService.generateResourceUrl(processedObjectName);
        this.processedImageCache.set(cacheKey, processedUrl);
        return processedUrl;
      }
      
      return null;
      
    } catch (error) {
      logger.error(`检查处理后图片失败: ${imageUrl}`, error);
      return null;
    }
  }
  
  /**
   * 预处理所有tap-talk图片
   */
  static async preprocessAllTapTalkImages(): Promise<void> {
    try {
      logger.info('[预处理] 开始预处理所有tap-talk图片');
      
      // 获取tap-talk配置
      const tapTalkConfig = await OssService.getObjectJsonContentWithCache<TapTalkConfig>('tap-talk');
      
      if (!tapTalkConfig || !tapTalkConfig.tapTalks) {
        logger.warn('[预处理] 未找到tap-talk配置');
        return;
      }
      
      const processingPromises: Promise<void>[] = [];
      
      // 遍历所有分类和项目
      for (const tapTalk of tapTalkConfig.tapTalks) {
        if (tapTalk.items && Array.isArray(tapTalk.items)) {
          for (const item of tapTalk.items) {
            if (item.detailImage && typeof item.detailImage === 'string') {
              // 生成原图URL
              const originalDetailImageUrl = OssService.generateResourceUrl(item.detailImage);
              
              // 添加到异步处理队列
              processingPromises.push(
                this.addBlurEffectAsync(originalDetailImageUrl)
              );
            }
          }
        }
      }
      
      logger.info(`[预处理] 共有 ${processingPromises.length} 张图片待处理`);
      
      // 等待所有异步处理完成（可选，如果不想阻塞启动，可以去掉这行）
      // await Promise.all(processingPromises);
      
      logger.info('[预处理] tap-talk图片预处理任务已启动');
      
    } catch (error) {
      logger.error('[预处理] 预处理tap-talk图片失败', error);
    }
  }
  
  /**
   * 处理单个图片文件（用于admin上传后处理）
   * @param imageObjectName 图片的OSS对象名称
   */
  static async processImageFile(imageObjectName: string): Promise<void> {
    try {
      // 检查是否为图片文件
      if (!this.isImageFile(imageObjectName)) {
        return;
      }
      
      logger.info(`[文件处理] 开始处理上传的图片: ${imageObjectName}`);
      
      // 生成图片URL
      const imageUrl = OssService.generateResourceUrl(imageObjectName);
      
      // 异步处理图片
      await this.addBlurEffectAsync(imageUrl);
      
      logger.info(`[文件处理] 图片处理任务已启动: ${imageObjectName}`);
      
    } catch (error) {
      logger.error(`[文件处理] 处理图片文件失败: ${imageObjectName}`, error);
    }
  }
  
  /**
   * 检查文件是否为图片
   * @param objectName 文件对象名称
   * @returns 是否为图片文件
   */
  private static isImageFile(objectName: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const extension = objectName.toLowerCase().split('.').pop();
    return extension ? imageExtensions.includes(`.${extension}`) : false;
  }
  
  /**
   * 下载图片
   * @param imageUrl 图片URL
   * @returns 图片Buffer
   */
  private static async downloadImage(imageUrl: string): Promise<Buffer> {
    try {
      // 如果是OSS URL，使用OSS客户端下载
      const objectName = OssService.extractObjectNameFromUrl(imageUrl);
      if (objectName) {
        const result = await OssService.getClient().get(objectName);
        return result.content as Buffer;
      }
      
      // 如果是其他URL，使用HTTP下载
      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer'
      });
      
      return Buffer.from(response.data);
      
    } catch (error) {
      logger.error(`下载图片失败: ${imageUrl}`, error);
      throw error;
    }
  }
  
  /**
   * 处理图片，添加模糊效果和叠加颜色
   * @param imageBuffer 原图片Buffer
   * @param blurRadius 模糊半径
   * @param overlayColor 叠加颜色
   * @returns 处理后的图片Buffer
   */
  private static async processImage(
    imageBuffer: Buffer, 
    blurRadius: number,
    overlayColor: { r: number, g: number, b: number, alpha: number }
  ): Promise<Buffer> {
    try {
      const image = sharp(imageBuffer);
      
      // 获取图片元数据
      const metadata = await image.metadata();
      const { width, height } = metadata;
      
      if (!width || !height) {
        throw new Error('无法获取图片尺寸');
      }
      
      // 创建叠加层
      const overlayBuffer = await sharp({
        create: {
          width,
          height,
          channels: 4,
          background: {
            r: overlayColor.r,
            g: overlayColor.g,
            b: overlayColor.b,
            alpha: overlayColor.alpha
          }
        }
      })
      .png()
      .toBuffer();
      
      // 应用模糊效果并叠加颜色
      const processedBuffer = await image
        .blur(blurRadius)
        .composite([{
          input: overlayBuffer,
          blend: 'over'
        }])
        .jpeg({
          quality: 85,
          progressive: true
        })
        .toBuffer();
      
      return processedBuffer;
      
    } catch (error) {
      logger.error('图片处理失败', error);
      throw error;
    }
  }
  
  /**
   * 生成缓存key
   * @param imageUrl 图片URL
   * @param blurRadius 模糊半径
   * @param overlayColor 叠加颜色
   * @returns 缓存key
   */
  private static generateCacheKey(
    imageUrl: string,
    blurRadius: number,
    overlayColor: { r: number, g: number, b: number, alpha: number }
  ): string {
    const data = `${imageUrl}_${blurRadius}_${overlayColor.r}_${overlayColor.g}_${overlayColor.b}_${overlayColor.alpha}`;
    return crypto.createHash('md5').update(data).digest('hex');
  }
  
  /**
   * 生成处理后的对象名称
   * @param originalObjectName 原对象名称
   * @param blurRadius 模糊半径
   * @param overlayColor 叠加颜色
   * @returns 处理后的对象名称
   */
  private static generateProcessedObjectName(
    originalObjectName: string,
    blurRadius: number,
    overlayColor: { r: number, g: number, b: number, alpha: number }
  ): string {
    const pathParts = originalObjectName.split('/');
    const fileName = pathParts.pop()!;
    const fileNameWithoutExt = fileName.split('.')[0];
    const ext = fileName.split('.').pop();
    
    const suffix = `_blur${blurRadius}_${overlayColor.r}-${overlayColor.g}-${overlayColor.b}-${Math.round(overlayColor.alpha * 100)}`;
    const processedFileName = `${fileNameWithoutExt}${suffix}.${ext}`;
    
    // 将处理后的图片放在processed目录下
    const processedObjectName = `processed/${pathParts.join('/')}/${processedFileName}`;
    
    return processedObjectName;
  }
  
  /**
   * 清理缓存
   */
  static clearCache(): void {
    this.processedImageCache.clear();
    this.processingQueue.clear();
    logger.info('图片处理缓存已清理');
  }
  
  /**
   * 获取缓存大小
   */
  static getCacheSize(): number {
    return this.processedImageCache.size;
  }
  
  /**
   * 获取处理队列大小
   */
  static getProcessingQueueSize(): number {
    return this.processingQueue.size;
  }
} 