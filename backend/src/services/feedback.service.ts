import { AppError } from '../middlewares/error.middleware';
import { UserFeedback } from '../models';
import User from '../models/user.model';
import { CreateFeedbackPayload } from '../types/feedback.type';
import logger from '../utils/logger';


export class FeedbackService {
  
  /**
   * 创建用户反馈
   * @param userId 用户ID
   * @param payload 反馈数据
   * @returns 创建的反馈记录
   */
  static async createFeedback(userId: number, payload: CreateFeedbackPayload): Promise<UserFeedback> {
    const { rating, content } = payload;

    // 验证评分范围
    if (rating < 1 || rating > 5) {
      throw new AppError('Rating must be between 1 and 5', 400);
    }

    // 验证用户是否存在
    const user = await User.findByPk(userId);
    if (!user) {
      throw new AppError('User not found', 404);
    }

    const feedback = await UserFeedback.create({
      userId: userId,
      rating,
      content: content?.trim() || ''
    });
    logger.info(`Feedback created: ${feedback}`);
    return feedback;
  }

} 