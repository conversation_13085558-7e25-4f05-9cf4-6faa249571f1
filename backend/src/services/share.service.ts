import { AppError } from "../middlewares/error.middleware";
import logger from "../utils/logger";
import { OssService } from "./oss.service";
import { WechatService } from "./wechat.service"
import { RedisService } from "./redis.service";
import { ShareConfig, ShareInfo } from "../types/share.type";
export class ShareService {
  static async shareInfo(userId: number, env: string):Promise<ShareInfo> {

    // todo check 过期问题
    const redisKey = `wxacodeDataURL:${userId}`;
    // 先尝试从 Redis 获取
    let wxacodeDataURL = await RedisService.get(redisKey);
  
    if (!wxacodeDataURL) {
      // 获取小程序码
      const wxacodeBuffer = await WechatService.getwxacodeunlimit(`userId=${userId}`, env)
      const wxacodeBase64 = wxacodeBuffer.toString('base64');
      wxacodeDataURL = `data:image/png;base64,${wxacodeBase64}`;
      // 缓存到 Redis，10分钟
      await RedisService.set(redisKey, wxacodeDataURL, 600);
    }
    // 并行获取图片和配置
    const shareConfig = await ShareService.getAssetsInfo();
    const [backgroundImageUrl, logoImageUrl] = await Promise.all([
        OssService.generateResourceUrl(shareConfig.backgroundImage),
        OssService.generateResourceUrl(shareConfig.logoImage),
    ]); 
    shareConfig.backgroundImage = backgroundImageUrl;
    shareConfig.logoImage = logoImageUrl;
    return {
      config: shareConfig,
      wxacodeDataURL
    };
  }

  private static async getAssetsInfo():Promise<ShareConfig>{
    try {
      const config = await OssService.getObjectJsonContentWithCache<ShareConfig>('share'); 
      return config;
    } catch (error) {
      logger.error('获取分享配置失败', error);
      throw new AppError('获取分享配置失败', 500);
    }
  }
}
