import { AppError } from "../middlewares/error.middleware";
import { Invitation, User } from "../models"; // 导入 User 模型
import logger from "../utils/logger";
// import { RedisService } from "./redis.service"; // 暂时不使用 Redis

export class InvitationService {
  /**
   * 创建邀请，如果已存在则返回现有邀请
   * @param inviterId 邀请者ID
   * @param inviteeId 被邀请者ID
   * @returns 邀请对象
   */
  static async createInvitation(inviterId: number, inviteeId: number) {
    logger.info("createInvitation", inviterId, inviteeId);
    if (inviterId == inviteeId) {
      throw new AppError("Inviter and invitee cannot be the same person.", 400);
    }

    // 查找或创建邀请记录
    // `findOrCreate` 会返回一个数组，第一个元素是找到或创建的实例，第二个元素是一个布尔值，表示是否是新创建的
    const [invitation, created] = await Invitation.findOrCreate({
      where: { inviterId, inviteeId },
      defaults: {
        inviterId,
        inviteeId,
      },
    });

    if (created) {
      logger.info(`New invitation created from inviter ${inviterId} to invitee ${inviteeId}`);
    } else {
      logger.info(`Invitation already exists from inviter ${inviterId} to invitee ${inviteeId}`);
    }

    return invitation;
  }

  /**
   * 根据被邀请者ID获取邀请记录列表
   * @param inviteeId 被邀请者用户ID
   * @returns 邀请记录列表，包含邀请者信息
   */
  static async getInvitationsByInviteeId(inviteeId: number): Promise<Invitation[]> {
    const invitations = await Invitation.findAll({
      where: { inviteeId },
      include: [
        {
          model: User,
          as: 'inviter', // 关联在 Invitation 模型中定义的别名
          attributes: ['id', 'username', 'avatarKey'], // 只选择需要的邀请者信息
        },
      ],
      order: [['createdAt', 'DESC']], // 按创建时间降序排列
    });
    return invitations;
  }

  /**
   * 根据邀请者ID获取邀请记录列表 (可选，如果需要的话)
   * @param inviterId 邀请者用户ID
   * @returns 邀请记录列表，包含被邀请者信息
   */
  static async getInvitationsByInviterId(inviterId: number): Promise<Invitation[]> {
    const invitations = await Invitation.findAll({
      where: { inviterId },
      include: [
        {
          model: User,
          as: 'invitee', // 关联在 Invitation 模型中定义的别名
          attributes: ['id', 'username', 'avatarKey'], // 只选择需要的被邀请者信息
        },
      ],
      order: [['createdAt', 'DESC']],
    });
    return invitations;
  }
}