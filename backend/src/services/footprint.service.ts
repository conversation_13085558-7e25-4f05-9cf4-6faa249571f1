import { AppError } from "../middlewares/error.middleware";
import { PlaySession, SuggestedGoal } from "../models";
import { EndGoalConfig, FeedbackConfig, FootprintConfig } from "../types/config.type";
import { FootprintInfo } from "../types/footprint.type";
import { Action } from "../types/partner-ai.types";
import { ActionService } from "./action.service";
import { OssService } from "./oss.service";
import { PlaySessionService } from "./play-session.service";
import { RedisService } from "./redis.service";
import { WechatService } from "./wechat.service";

export class FootprintService {

    static async getFootprintInfo(userId: number, playSessionId: number, env: string):Promise<FootprintInfo> {
        const playSession = await PlaySession.findByPk(playSessionId)
        if(!playSession) {
            throw new AppError('会话不存在', 404)
        }
        
        const selectedGoals = await SuggestedGoal.findAll({
            where: { playSessionId: playSessionId, isSelected: true }
        });
        if (!selectedGoals || selectedGoals.length === 0) {
            throw new AppError('No selected goal found for this session. Please select a goal first.', 400);
        }
        const primaryGoal = selectedGoals[0];

        // 并行获取所有配置
        const [config, endGoalConfig, feedbackConfig] = await Promise.all([
            OssService.getObjectJsonContentWithCache<FootprintConfig>('footprint'),
            OssService.getObjectJsonContentWithCache<EndGoalConfig>('end-goal'),
            OssService.getObjectJsonContentWithCache<FeedbackConfig>('feedback')
        ]);

        if(!config) {
            throw new AppError('footprint配置不存在', 404)
        }
        if(!endGoalConfig) {
            throw new AppError('end-goal配置不存在', 404)
        }
        if(!feedbackConfig) {
            throw new AppError('feedback配置不存在', 404)
        }

        const goalConfig = endGoalConfig.goalConfigs.filter(goal => goal.goalKey === primaryGoal.goalKey)[0];
        if(!goalConfig) {
            throw new AppError(`${primaryGoal.goalKey}配置不存在`, 404)
        }
        
        let respActions = JSON.parse(playSession.respAction || '[]') as Action[];
        const actionsPromise = respActions.length === 0 ? ActionService.actions(playSessionId) : Promise.resolve(respActions);

        // 获取小程序码的逻辑
        const redisKey = `wxacodeDataURL:no_scene`;
        const wxacodePromise = RedisService.get(redisKey).then(async (cachedWxacode) => {
            if (cachedWxacode) {
                return cachedWxacode;
            }
            // 获取小程序码
            const wxacodeBuffer = await WechatService.getwxacodeunlimit('no_scene', env);
            const wxacodeBase64 = wxacodeBuffer.toString('base64');
            const wxacodeDataURL = `data:image/png;base64,${wxacodeBase64}`;
            // 缓存到 Redis，10分钟
            await RedisService.set(redisKey, wxacodeDataURL, 600);
            return wxacodeDataURL;
        });

        // 并行执行真正的异步操作（数据库查询、网络请求等）
        const [finishedGoals, actions, wxacodeDataURL] = await Promise.all([
            PlaySessionService.getFinishedGoalsCount(userId, primaryGoal.goalKey),
            actionsPromise,
            wxacodePromise,
        ]);

        // 串行执行本地操作（generateSignedUrl）
        config.closeIcon = OssService.generateResourceUrl(config.closeIcon);
        config.gradientMask = OssService.generateResourceUrl(config.gradientMask);
        config.giftIcon = OssService.generateResourceUrl(config.giftIcon);
        config.lightbulbIcon = OssService.generateResourceUrl(config.lightbulbIcon);
        config.glitchImage = OssService.generateResourceUrl(config.glitchImage);
        config.modalButtonAreaImage = OssService.generateResourceUrl(config.modalButtonAreaImage);
        config.saveButtonIcon = OssService.generateResourceUrl(config.saveButtonIcon);
        
        feedbackConfig.starSelectedIcon = OssService.generateResourceUrl(feedbackConfig.starSelectedIcon);
        feedbackConfig.starUnSelectedIcon = OssService.generateResourceUrl(feedbackConfig.starUnSelectedIcon);
        
        // 计算 isAllAchieved：所有目标 count 都大于 0
        const isAllAchieved = finishedGoals.every(goal => goal.count > 0);
        
        return {
            config: {
                ...config,
                mainColor: goalConfig.mainColor,
                modalMpCode: wxacodeDataURL
            },
            feedbackConfig,
            finishedGoals,
            actions: actions,
            isAllAchieved: isAllAchieved,  
        }
    }
}