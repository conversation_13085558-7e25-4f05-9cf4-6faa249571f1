import jwt from 'jsonwebtoken';
import { TokenPayload } from '../types/auth.type';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export class TokenUtil {
  static generateToken(payload: TokenPayload): string {
    return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });
  }

  static verifyToken(token: string): TokenPayload {
    return jwt.verify(token, JWT_SECRET) as TokenPayload;
  }
}