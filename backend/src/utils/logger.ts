import winston from 'winston';

const isDevelopment = process.env.NODE_ENV === 'development';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
     format: winston.format.combine(
      winston.format.timestamp({
        format: () => new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })
      }),
       winston.format.json()
     ),
     transports: [
       ...(isDevelopment ? [new winston.transports.Console()] : []),
       new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
       new winston.transports.File({ filename: 'logs/combined.log' })
     ]
});

export default logger;