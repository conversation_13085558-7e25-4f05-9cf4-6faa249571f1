import { PlaySession, DetectedEmotion, SuggestedGoal } from '../models';

export interface PlaySessionEntryResult {
  backgroundImage: string;
  backgroundVideo: string;
  recommendedPrompts: string[];
}

export interface EntryConfig {
  bgImg: string;
  bgVideo: string;
}

export interface SubmitPlaySessionResult {
  playSessionId: number;
}

export interface DetectedEmotionExtra extends DetectedEmotion {
  icon: string;
  iconSelected: string;
}

export type PlaySessionDetail = PlaySession & {
  detectedEmotions?: DetectedEmotionExtra[];
  suggestedGoals?: SuggestedGoal[];
};

export interface PlayInfo {
  goalKey: string;
  tagIcon: string;
  mainColor:string;
  goalDescription: string;
  preAudioUrl: string;
  seamAudioUrl: string;
  goalImage: string;
  glitchImage: string;
  endAudioUrl: string;
  pauseButtonIcon: string;
  playButtonIcon: string;
  leavingModalTitle:string;
  leaveButtonText:string;
  cancelButtonText:string;
}

export interface BridgeInfo {
  lottieAnimation1: string;
  lottieAnimation2: string;
  guideText: string;
  buttonText: string;
}
