import { GenerateQuoteResult } from './partner-ai.types';

export interface QuoteInfo {
      quote: string;
      author: string;
      mainIcon: string;
      backgroundImage: string;
      buttonText: string;
      mainColor:string;
      saveButtonIcon: string;
      modalTitle: string;
      modalSaveButtonText: string;
      modalShareButtonText: string;
      modalCanvasTitle: string;
      modalCanvasSlogan: string;
      glitchImage:string;
      centerMaskImage:string;
      modalButtonAreaImage: string;
      modalMpCode: string;
      originalQuoteResult?: GenerateQuoteResult; // 原始AI返回的quote结果
      selectedIndex?: number; // 选中的quote在原始数组中的索引
}