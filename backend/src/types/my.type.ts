import { UserFullProfile } from "./user.type";

export interface MyConfig {
      membershipCard: {
            cardBackgroundImage: string;
            cardExpiredBackgroundImage: string;
            cardLifetimeBackgroundImage: string;
            cardTextImage:string;
      };
      inviteGuide: string,
      rightArrowIcon:string,
      defaultAvatar: string,
      defaultUsername: string,
      aboutPdfLink:string,
      systemPdfLink:string
}

export interface MyInfo {
      config: MyConfig;
      userInfo: UserFullProfile;
}

export interface PdfConfig {
      aboutPdfLink:string,
      systemPdfLink:string
}