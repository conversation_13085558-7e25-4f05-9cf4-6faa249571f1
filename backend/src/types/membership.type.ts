import { MembershipType } from "../enums";

export interface Feature {
      icon: string;
      title: string;
      desc: string;
}

export interface Plan {
      key: string;
      type: string;
      title: string;
      unit: string;
      desc?: string;
      price: string;
}

export interface MembershipConfig {
      title: string;
      activedTitle: string;
      subtitle: string;
      activedButtonImage:string;
      features: Feature[];
      plans: Plan[];
      buttonText: string;
}

// 会员状态接口
export interface UserMembershipStatus {
  userId: number;
  membershipType: MembershipType;
  expireDate: Date | null;
  isAutoRenew: boolean;
  premiumExpireDate: Date | null;
  trialExpireDate: Date | null;
  displayText: string;
  isActive: boolean;
  remainingDays: number | null; // 剩余有效天数，null表示无限期（如终身会员）
}