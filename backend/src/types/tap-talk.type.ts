export interface TapTalkDetail{ 
      name: string
      image: string
      audio: string
      glichyImage: string
      buttonText: string
      leaveButtonText: string
      cancelButtonText: string
      leavingConfirmText: string
      nextIcon: string
      scriptContent: TapTalkPage[]
}

export interface TapTalk{
      category: string
      description: string
      buttonText: string
      audios: string[]
      items: TapTalkItem[]
  }
  
export interface TapTalkItem{
      id: number
      name: string
      image: string
      detailImage: string
      script: string
      scriptContent: TapTalkPage[]
}
  
export interface TapTalkPage{
      page: string
      content: string
      command: string
}