import { FeedbackConfig, FootprintConfig } from "./config.type";
import { Action } from "./partner-ai.types";

export interface FootprintInfo {
    config: FootprintConfig & {
        mainColor: string;
        modalMpCode: string;
    }
    feedbackConfig: FeedbackConfig,
    finishedGoals: FinishedGoal[]
    actions: Action[],
    isAllAchieved: boolean,
}

export interface FinishedGoal {
    goalKey: string;
    count: number;
    firstAchieved: boolean;
    currentAchieved: boolean;
}

