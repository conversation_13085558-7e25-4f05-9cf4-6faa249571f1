import { TapTalk } from "./tap-talk.type";

export interface HomepageConfig {
    backgroundVideo: string;
    guideButtonText: string;
    guideButtonIcon: string;
    guideTextImage: string;
    closeIcon: string;
    guideText1: string;
    guideText2: string;
    startButtonText: string;
}

export interface EditPageConfig {
    placeHolderText: string;
    buttonText: string;
    speakingGif: string;
    keyboardIcon: string;
    voiceIcon: string;
    confirmArrowIcon: string;
    confirmText: string;
    loadingText1: string;
    loadingText2: string;
    bottomMask: string;
}
export interface CheckInConfig {
    guideText: string;
    confirmButtonText: string;
    checkedIcon: string;
    uncheckedIcon: string;
    emotionBackgroundImage: string;
    unSelectedEmotionBackgroundImage: string;
}

export interface EndGoalConfig {
    title: string;
    buttonText: string;
    maskImage: string;
    infoIcon: string;
    goalConfigs: GoalConfig[];
}
export interface GoalConfig {
    mainColor: string;
    description: string;
    example: string;
    goalKey: string;
    quote: string;
    scenario: string;
    tag: string;
    tagIcon: string;
    displayCardBackgroundImage: string;
    displayBackgroundImageKey: string;
}
export interface BridgeConfig {
    guideText: string;
    buttonText: string;
    lottieAnimationTemplate: string;
}
export interface PlayConfig {
    seamAudioUrl: string;
    endAudioUrl: string;
    pauseButtonIcon: string;
    playButtonIcon: string;
    glitchImage: string;
    leavingModalTitle:string;
    leaveButtonText:string;
    cancelButtonText:string;
}

export interface QuoteConfig {
    saveButtonIcon: string;
    buttonText: string;
    modalTitle: string;
    modalSaveButtonText: string;
    modalShareButtonText: string;
    modalCanvasTitle: string;
    modalCanvasSlogan: string;
    modalButtonAreaImage: string;
    glitchImage:string;
    centerMaskImage:string;
}

export interface CommonConfig {
    mainIcon: string;
    mainIconNofill: string;
}

export interface FootprintConfig {
    tab1Text: string
    tab2Text: string
    firstAchievedText: string
    achievedText: string
    tips1: string
    tips2: string
    tips2Title: string
    doneButtonText: string
    receiveButtonText: string
    closeIcon: string
    gradientMask: string
    lightbulbIcon: string
    saveButtonIcon: string
    modalSaveButtonText: string
    modalShareButtonText: string
    modalTitle: string
    modalCanvasTitle: string
    modalCanvasSlogan: string
    modalButtonAreaImage: string,
    glitchImage:string
    giftIcon:string

}
export interface FeedbackConfig {
    feedbackTitle: string
    feedbackPlaceHoder: string
    buttonText: string
    starSelectedIcon: string
    starUnSelectedIcon: string
    contentLimitLength: number
}

export interface OnboardingConfig {
    buttonText: string
    entryText1: string
    entryText2: string
    startText1: string
    startText2: string
    startButtonText: string
    startImage: string
    logoIcon: string
    steps: OnboardingStepConfig[]
    backgroundVideo: string
    soundSetting: SoundSettingConfig
}

export interface OnboardingStepConfig {
    text1: string
    text2: string
    image: string
}

export interface SoundSettingConfig {
    buttonText: string
    guideText: string
    auditionText: string
    auditionIcon: string
    auditionPauseIcon: string
    rippleGif:string
    rippleIcon:string
    leftArrowDarkIcon: string
    leftArrowIcon: string
    rightArrowDarkIcon: string
    playingGif: string
    playingIcon: string
    rightArrowIcon: string
    figures: FigureConfig[]
}

export interface FigureConfig {
    id: number
    refId: string
    name: string
    avatarImage: string
    soundDemo: string
    introduce1: string
    introduce2: string
    isDefault: boolean
}

export interface ExploreConfig{
    titleImage: string
    bottomText: string
    items: ExploreItemConfig[]
}

export interface ExploreItemConfig{
    key: string
    title1Text: string
    title2Text: string,
    buttonText: string,
    backgroundImage: string
}

export interface TapTalkConfig{
    navbarTitle: string
    bottomIcon: string
    nextIcon: string
    enterIcon: string
    leaveButtonText: string
    cancelButtonText: string
    leavingConfirmText: string
    glichyImage: string;
    tapTalks: TapTalk[];
    audioPlayingIcon:string;
    audioPausedIcon:string;
    overlayImage:string;
}

export interface DivinationConfig{
    backgroungImage: string;
    titleTextImage: string;
    guideTextImage: string;
    infoIcon: string;
    loadingIcon: string;
    loadingBackgroundImage: string;
    shareBackgroundImage:string;
    shareCardBackgroundImage:string;
    shareHeaderImage: string;
    shareSubtitle1Image: string;
    shareSubtitle2Image: string;
    shareSubtitle3Image: string;
    mainDarkIcon:string;
    shareSloganImage:string;
    shareBottomTitle: string;
    shareBottomSlogan: string;
    questionLabel: string;
    birthDateLabel: string;
    infoTips: string;
    tips: string;
    startButtonText: string;
    loadingText: string;
    saveImageButtonText: string;
    shareButtonText: string;
    limitCount: number;
    troubleshoot1Title: string;
    troubleshoot1Content: string;
    troubleshoot2Title: string;
    troubleshoot2Content: string;
    noticedButtonText: string;
}