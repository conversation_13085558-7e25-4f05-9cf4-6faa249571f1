export interface Emotion {
  emotion: string;
  explanation: string;
  symbol: number;
}
export interface Goal {
  channel: string;
  short: string;
  long: string;
} 
export interface AnalyzeEmotionResult {
  emotions: Emotion[];
}

export interface SetGoalsResult {
  goals: Goal[];
}
export interface EmpathyResult {
  greeting: string;
  empathy: string;
  goal: string;
}
// export interface ProcessWorkflowResult {
//   results: GeneratedTextResult[];
// }

export interface GenerateVoiceResult {
  audioStream?: Buffer;       // 音频数据流
  audioUrl?: string;          // 或者音频URL (两种方式二选一)
  contentType: string;        // 音频MIME类型，如 'audio/mp3'
  duration: number;           // 音频时长（秒）
  text: string;               // 音频对应的文本内容
}

export interface InteractivePage {
  page_number: number;
  content: string;
}

export interface GenerateInteractiveParam {
  empathy_text: EmpathyResult;
  workflow_text: ProcessWorkflowResultItem[];
}
export interface GenerateInteractiveResult {
  pages: InteractivePage[];
}

export interface Quote {
  quote: string;
  author: string;
}
export interface GenerateQuoteResult {
  quotes: Quote[];
}
export interface GenerateActionsResult {
  actions: Action[];
}

export interface Action{
  emoji: string;
  name: string;
  description: string;
}

export interface GenerateEmpathyParams {
  text: string;
  goal: string; // 必须是 set-goals 接口返回的类别中的一种
  emotion: string; // 用户的情绪描述，例如："焦虑:任务堆得像山，总感觉喘不过气。"
  no_emotion: string; // 用户没选择的情绪描述，例如："焦虑:任务堆得像山，总感觉喘不过气。"
  voice_id: string; // 用户选择的语音id
  output_style?: 'audio' | 'text'; // 默认为 'audio'
  generate_audio?: 'false' | 'true'; // 默认为 false，output_style 为 'audio' 且需要音频时必须为 true
}

export interface EmpathyAudioResponse {
  greeting: string;
  empathy: string;
  goal: string;
  paused_text: string;
  audio_base64?: string; // 根据文档，audio风格下才有，设为可选
}

export interface EmpathyTextPage {
  page_num: number;
  content: string;
}

export interface EmpathyTextResponse {
  pages: EmpathyTextPage[];
}

// export type GenerateEmpathyResult = EmpathyAudioResponse | EmpathyTextResponse;
export interface GenerateEmpathyResult {
  text: string;
  tts_text: string;
  audio: string;
}

export interface ProcessWorkflowParams {
  text: string;
  goal: string; // 用户选择的目标
  emotion: string; // 用户的情绪
  no_emotion: string; // 用户没选择的情绪描述，例如："焦虑:任务堆得像山，总感觉喘不过气。"
  voice_id: string; // 用户选择的语音id
  output_type?: 'audio' | 'text'; // 默认为 'audio'
  generate_audio?: string; // 默认为 false，output_style 为 'audio' 且需要音频时必须为 true
}

// 用于 paged_text 的页面结构
export interface WorkflowTextPage {
  page_num: number;
  content: string;
}

// ProcessWorkflow 响应中 'results' 数组的元素类型 (Audio 风格)
export interface ProcessWorkflowAudioResultItem {
  tag: string;
  generated_text: string;
  audio_text: string;
  audio_base64?: string; // 根据文档，audio风格下才有，设为可选
}

// ProcessWorkflow 响应中 'results' 数组的元素类型 (Text 风格)
export interface ProcessWorkflowTextResultItem {
  tag: string;
  generated_text: string;
  paged_text: WorkflowTextPage[];
}

// ProcessWorkflow 响应中 'results' 数组元素的联合类型
export type ProcessWorkflowResultItem = ProcessWorkflowAudioResultItem | ProcessWorkflowTextResultItem;

// ProcessWorkflow 接口的整体响应结构
export interface ProcessWorkflowResponseWrapper {
  success: boolean;
  results: ProcessWorkflowResultItem[];
}
export interface ProcessWorkflowResult {
  text: string;
  tts_text: string;
  description: string;
  audio: string;
}


// --- Processed types for workflow with audio URLs ---

export interface ProcessWorkflowAudioResultItemWithUrl extends Omit<ProcessWorkflowAudioResultItem, 'audio_base64'> {
  audio_url?: string;
}

export type ProcessedProcessWorkflowResultItem = ProcessWorkflowAudioResultItemWithUrl | ProcessWorkflowTextResultItem;

export interface ProcessedProcessWorkflowResponseWrapper {
  success: boolean;
  results: ProcessedProcessWorkflowResultItem[];
  generated_at?: string;
  version?: number;
}


export interface EmpathyAudioResponseWithUrl extends Omit<EmpathyAudioResponse, 'audio_base64'> {
  audio_url?: string;
}

export interface ProcessedGenerateEmpathyResult {
    text: string;
    tts_text: string;
    audio_url: string;
    generated_at?: string;
    version?: number;
    ai_call_duration?: number; // AI调用耗时，单位：毫秒
}

export interface ProcessedProcessWorkflowResult {
    text: string;
    tts_text: string;
    description: string;
    audio_url: string;
    generated_at?: string;
    version?: number;
    ai_call_duration?: number; // AI调用耗时，单位：毫秒
}

// 内部使用的带时间戳版本（用于缓存检查）
export interface ProcessedGenerateEmpathyResultWithTimestamp extends ProcessedGenerateEmpathyResult {
    generated_at: string;
    version?: number;
}

// 占卜相关类型定义
export interface GenerateZhanbuParams {
  text: string;
}

export interface ZhanbuPayload {
  insight: string[];
  advice: string[];
  lucky: string;
}

export interface GenerateZhanbuResult {
  fulfillment: boolean;
  reply: string;
  guard: Guard;
  payload: ZhanbuPayload;
}

export interface Guard {
  flagged: boolean;
  case_id: string;
  type: string;
  response: string;
}

// 语音相关类型定义
export interface Voice {
  id: number;
  name: string;
  voice_id: string;
  gender: string;
  category: string | null;
  created_at: string;
  updated_at: string;
}

export interface GetVoiceListResult {
  voices: Voice[];
}

export type VoiceListResult = Voice[];

// 用于存储完整的quote响应信息
export interface QuoteResponse {
  selectedQuote: Quote;
  originalQuoteResult: GenerateQuoteResult;
  selectedIndex: number; // 记录选中的quote在原始数组中的索引
  generatedAt: string;   // 生成时间
}