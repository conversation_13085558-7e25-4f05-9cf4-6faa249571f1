import { Router } from 'express';
import { PlaySessionController } from '../controllers/play-session.controller';
import { authMiddleware } from '../middlewares/auth.middleware';

const router = Router();

router.post('/:playSessionId/generate-empathy', authMiddleware, PlaySessionController.generateEmpathy)
router.get('/:playSessionId/empathy-audio', PlaySessionController.getEmpathyAudio)
router.get('/:playSessionId/get-play-info', authMiddleware, PlaySessionController.getPlayInfo)
router.get('/:playSessionId/workflow-status', authMiddleware, PlaySessionController.getWorkflowStatus);
router.get('/:playSessionId/workflow-audio', PlaySessionController.getWorkflowAudio)
router.put('/:playSessionId/status', authMiddleware, PlaySessionController.updateStatus)
router.put('/:playSessionId/finish', authMiddleware, PlaySessionController.finish)

router.post('/', authMiddleware, PlaySessionController.submit)
router.get('/:playSessionId', authMiddleware, PlaySessionController.get)

router.get('/:playSessionId/bridge-info', authMiddleware, PlaySessionController.getBridgeInfo)

export default router;