import { Router } from 'express';
import { <PERSON>otion<PERSON>ontroller } from '../controllers/emotion.controller';
import { authMiddleware } from '../middlewares/auth.middleware';

const router = Router();

router.put('/:playSessionId', authMiddleware, EmotionController.updateMultipleEmotions);
router.get('/:playSessionId', authMiddleware, EmotionController.getEmotionsByPlaySession);

export default router;