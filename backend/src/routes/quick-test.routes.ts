import express from 'express';
import { QuickTestController } from '../controllers/quick-test.controller';
import multer from 'multer'; // 需要安装: npm install multer @types/multer

const router = express.Router();
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 1024 * 1024 // 限制1MB
  }
});

router.get('/stream-tts-real', QuickTestController.streamTtsReal);

// 添加测试获取token的路由
router.get('/test-token', QuickTestController.testGetToken);

// 添加测试语音识别的路由
router.post('/test-voice-recognition', 
 upload.single('voice'),
  QuickTestController.testVoiceRecognition
);

export default router;