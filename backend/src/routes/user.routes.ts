import { Router } from 'express';

import UserController from '../controllers/user.controller';
import { authMiddleware } from '../middlewares/auth.middleware';

const router = Router();

router.get('/', authMiddleware, UserController.getProfile);
router.put('/', authMiddleware, UserController.updateProfile);
router.get('/full', authMiddleware, UserController.getFullProfile);
router.put('/onboarding', authMiddleware, UserController.updateOnboarding);
router.get('/bind-phone', authMiddleware, UserController.bindPhoneInfo)
router.post('/bind-phone', authMiddleware, UserController.bindPhone)

export default router;