import { Router } from 'express';
import { authMiddleware } from '../middlewares/auth.middleware';
import { ConfigController } from '../controllers/config.controller';

const router = Router();

router.get('/homepage', authMiddleware, ConfigController.homepage);

router.get('/edit-page', authMiddleware, ConfigController.editpage);

router.get("/check-in", authMiddleware, ConfigController.checkIn)

router.get("/end-goal", authMiddleware, ConfigController.endGoal)

router.get("/bridge", authMiddleware, ConfigController.bridge)

router.get("/onboarding", authMiddleware, ConfigController.onboarding)

router.get("/explore", authMiddleware, ConfigController.explore)

router.get("/tap-talk", authMiddleware, ConfigController.tapTalk)

router.get("/pdf", authMiddleware, ConfigController.pdf)

export default router;