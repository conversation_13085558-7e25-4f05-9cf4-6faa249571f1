import { Router } from 'express';
import { UploadController } from '../controllers/upload.controller';
import multer from 'multer';
import { authMiddleware } from '../middlewares/auth.middleware';

const router = Router();
const uploadController = new UploadController();

const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 限制文件大小为 5MB
  },
  fileFilter: (req, file, cb) => {
    // 只允许上传音频文件
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传音频文件'));
    }
  }
});

router.get('/signature', uploadController.signature.bind(uploadController));
router.post('/audio', authMiddleware, upload.single('audio'), uploadController.uploadAudio.bind(uploadController));

export default router;