import { Router } from 'express';
import { AdminController } from '../controllers/admin.controller';
import { authAdminMiddleware } from '../middlewares/auth.admin.middleware';
import { uploadSingle } from '../middlewares/upload.middleware';

const router = Router();

// 登录接口 - 不需要认证
router.post('/login', AdminController.login);

// 验证token - 需要认证
router.post('/verify-token', authAdminMiddleware, AdminController.verifyToken);

// 以下接口都需要认证
router.get('/config/:pageName', authAdminMiddleware, AdminController.getConfig);

router.put('/config/:pageName', authAdminMiddleware, AdminController.updateConfig);

router.post('/resource/:pageName/upload', authAdminMiddleware, uploadSingle, AdminController.updateResource);

router.post('/script-content', authAdminMiddleware, AdminController.getScriptContent);

// 系统状态管理
router.get('/system-status', authAdminMiddleware, AdminController.getSystemStatus);

router.put('/system-status', authAdminMiddleware, AdminController.updateSystemStatus);

// 批量发放用户奖励
router.post('/grant-batch-rewards', authAdminMiddleware, AdminController.grantBatchRewards);

export default router;