
export enum MembershipRenewalType {
  NONE = 'NONE',
  AUTO_RENEW = 'AUTO_RENEW',
  LIFETIME = 'LIFETIME'
}

export enum MembershipPlanKey {
  PREMIUM_LIFETIME = 'premium_lifetime',
  PREMIUM_MONTHLY_AUTO_RENEW = 'premium_monthly_auto_renew',
  PREMIUM_MONTHLY = 'premium_monthly'
}

export enum MembershipType {
  GUEST = 'GUEST',       // 游客
  TRIAL = 'TRIAL',       // 体验会员
  PREMIUM = 'PREMIUM',   // 付费会员
  LIFETIME = 'LIFETIME'  // 终身会员
}
