import multer from 'multer';
import path from 'path';

// 配置multer存储为内存存储
const storage = multer.memoryStorage();

// 文件类型过滤器
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // 允许的文件类型
  const allowedMimeTypes = [
    'image/jpeg',
    'image/png', 
    'image/gif',
    'image/svg+xml',
    'audio/mpeg',
    'audio/wav',
    'audio/mp3',
    'application/json',
    'text/plain',
    'video/mp4',
    'video/webm'
  ];

  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(null, false);
  }
};

// 配置multer
export const upload = multer({
  storage: storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB 限制
  },
  fileFilter: fileFilter
});

// 单文件上传中间件
export const uploadSingle = upload.single('file'); 