import { Request, Response, NextFunction } from 'express';

export const responseWrapper = (req: Request, res: Response, next: NextFunction) => {
  const originalJson = res.json;
  
  // 设置响应头，确保正确的字符编码
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  
  // 重写 res.json 方法
  res.json = function(data: any) {
    // 如果数据已经是标准格式就直接发送
    if (data && 'code' in data && 'msg' in data && 'data' in data) {
      return originalJson.call(this, data);
    }
    
    // 否则包装成标准格式
    return originalJson.call(this, {
      code: 0,
      msg: 'ok',
      data: data.data
    });
  };
  
  next();
};