import { Request, Response, NextFunction } from 'express';
import { OssService } from '../services/oss.service';
import logger from '../utils/logger';

// 需要处理的URL字段名列表
const URL_FIELDS = ['imageUrl', 'videoUrl', 'thumbnailUrl', 'coverUrl', 'url'];

/**
 * 处理响应中的OSS URL的中间件
 */
export const processOssUrls = () => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // 保存原始的res.json方法
    const originalJson = res.json;
    
    // 重写res.json方法
    res.json = function(data: any) {
      // 处理数据并返回响应
      OssService.processOssUrls(data, URL_FIELDS)
        .then(processedData => {
          originalJson.call(res, processedData);
        })
        .catch(error => {
          logger.error('处理OSS URL失败:', error);
          originalJson.call(res, data);
        });
      
      // 返回res对象以支持链式调用
      return res;
    } as any; // 使用类型断言解决类型不匹配问题
    
    next();
  };
};

/**
 * 查找对象中所有需要处理的OSS URL
 */
function findOssUrls(obj: any, currentPath: (string|number)[], result: {path: (string|number)[], value: string}[]): void {
  if (!obj || typeof obj !== 'object') return;
  
  if (Array.isArray(obj)) {
    // 处理数组
    obj.forEach((item, index) => {
      if (typeof item === 'object' && item !== null) {
        findOssUrls(item, [...currentPath, index], result);
      }
    });
  } else {
    // 处理对象
    for (const key of Object.keys(obj)) {
      const value = obj[key];
      
      if (URL_FIELDS.includes(key) && typeof value === 'string' && value.includes('.aliyuncs.com')) {
        // 找到需要处理的URL
        result.push({
          path: [...currentPath, key],
          value
        });
      } else if (typeof value === 'object' && value !== null) {
        // 递归处理嵌套对象
        findOssUrls(value, [...currentPath, key], result);
      }
    }
  }
}

/**
 * 根据路径设置对象中的值
 */
function setValueAtPath(obj: any, path: (string|number)[], value: any): void {
  let current = obj;
  
  for (let i = 0; i < path.length - 1; i++) {
    current = current[path[i]];
  }
  
  current[path[path.length - 1]] = value;
}