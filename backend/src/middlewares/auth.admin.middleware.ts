import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AppError } from './error.middleware';
import logger from '../utils/logger';

// 简单的管理员用户配置
const ADMIN_USERS = {
  admin: 'm$i4un5ggi9#4Dg3'
};

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-key-for-admin-auth';

export interface AdminJwtPayload {
  username: string;
  role: 'admin';
  iat?: number;
  exp?: number;
}

// 验证管理员用户名密码
export const verifyAdminCredentials = (username: string, password: string): boolean => {
  return ADMIN_USERS[username as keyof typeof ADMIN_USERS] === password;
};

// 生成JWT token
export const generateAdminToken = (username: string): string => {
  const payload: AdminJwtPayload = {
    username,
    role: 'admin'
  };
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '24h' });
};

// 验证JWT token
export const verifyAdminToken = (token: string): AdminJwtPayload => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as AdminJwtPayload;
    return decoded;
  } catch (error) {
    throw new AppError('无效的访问令牌', 401);
  }
};

// 认证中间件
export const authAdminMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AppError('未提供访问令牌', 401);
    }

    const token = authHeader.substring(7); // 移除 "Bearer " 前缀
    const decoded = verifyAdminToken(token);
    
    // 将用户信息附加到请求对象
    (req as any).admin = decoded;
    
    logger.info(`[Admin] Authenticated user: ${decoded.username}`);
    next();
  } catch (error) {
    if (error instanceof AppError) {
      next(error);
    } else {
      next(new AppError('认证失败', 401));
    }
  }
};