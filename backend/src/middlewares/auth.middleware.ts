import { Request, Response, NextFunction } from 'express';
import { AuthRequest, TokenPayload } from '../types';
import { TokenUtil } from '../utils/token.util';
import { AppError } from './error.middleware';

export const authMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
      throw new AppError('未提供访问令牌', 401);
    }

    const decoded = TokenUtil.verifyToken(token);
    (req as AuthRequest).user = decoded;
    
    next();
  } catch (error) {
    next(new AppError('无效的访问令牌', 401));
  }
};