import { Request, Response, NextFunction } from 'express';
import { SystemStatusService } from '../services/system-status.service';
import { ERROR_CODES } from '../constants/error-codes';
import logger from '../utils/logger';

/**
 * 系统状态检查中间件
 * 在处理小程序请求前检查系统是否可用
 * 如果系统不可用，直接返回维护状态错误
 */
export const systemStatusMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // 检查系统状态
    // const systemStatus = await SystemStatusService.getSystemStatus();

    // if (!systemStatus.isAvailable) {
    //   // 系统不可用，返回维护状态
    //   logger.info(`系统维护中，拒绝请求: ${req.method} ${req.path}`);

    //   res.status(503).json({
    //     code: ERROR_CODES.SYSTEM_MAINTENANCE,
    //     msg: systemStatus.message || '系统正在维护中，请稍后再试',
    //     data: null
    //   });
    //   return;
    // }

    next();
  } catch (error) {
    logger.warn('检查系统状态时发生错误，允许请求继续:', error);
    next();
  }
};

/**
 * 创建系统状态中间件的工厂函数
 * 可以传入配置选项来自定义行为
 */
export const createSystemStatusMiddleware = (options?: {
  // 是否在错误时阻止请求（默认false，即出错时允许请求继续）
  blockOnError?: boolean;
  // 自定义错误消息
  defaultMessage?: string;
}) => {
  const { blockOnError = false, defaultMessage = '系统正在维护中，请稍后再试' } = options || {};
  
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const systemStatus = await SystemStatusService.getSystemStatus();
      
      if (!systemStatus.isAvailable) {
        logger.info(`系统维护中，拒绝请求: ${req.method} ${req.path}`);
        
        return res.status(503).json({
          code: ERROR_CODES.SYSTEM_MAINTENANCE,
          msg: systemStatus.message || defaultMessage,
          data: null
        });
      }
      
      next();
    } catch (error) {
      logger.warn('检查系统状态时发生错误:', error);
      
      if (blockOnError) {
        // 如果配置为出错时阻止请求，返回服务不可用
        return res.status(503).json({
          code: ERROR_CODES.SERVICE_UNAVAILABLE,
          msg: '系统状态检查失败，请稍后再试',
          data: null
        });
      }
      
      // 默认行为：出错时允许请求继续
      next();
    }
  };
};
