import { Request, Response, NextFunction } from 'express';
import { SystemStatusService } from '../services/system-status.service';
import logger from '../utils/logger';

export class SystemStatusController {
  
  /**
   * 检查系统状态 - 供小程序调用
   * 不需要认证，公开接口
   */
  static async checkSystemStatus(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info('小程序检查系统状态');
      
      const systemStatus = await SystemStatusService.getSystemStatus();
      
      res.json({
        success: true,
        data: {
          isAvailable: systemStatus.isAvailable,
          message: systemStatus.message
        }
      });
    } catch (error) {
      logger.error('检查系统状态时发生错误:', error);
      // 即使发生错误，也返回系统可用，避免影响小程序正常使用
      res.json({
        success: true,
        data: {
          isAvailable: true,
          message: ''
        }
      });
    }
  }
}
