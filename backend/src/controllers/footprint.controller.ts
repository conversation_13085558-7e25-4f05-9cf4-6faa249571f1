import { Response, NextFunction } from 'express';
import { AuthRequest } from '../types';
import { AppError } from '../middlewares/error.middleware';
import { FootprintService } from '../services/footprint.service';

export class FootprintController {
  
  static async getFootpritnInfo(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      let userId = req.user?.userId;
      if(!userId){
        throw new AppError('用户未登录', 401);
      }
      const env = req.query.env as string;
      const { playSessionId } = req.params; 
      const quote = await FootprintService.getFootprintInfo(userId, Number(playSessionId), env)
      res.json({
        success: true,
        data: quote
      });
    } catch (error) {
      next(error);
    }
  }
}