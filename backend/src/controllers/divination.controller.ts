import { Response, NextFunction } from 'express';
import { DivinationService } from '../services/divination.service';
import { AuthRequest } from '../types/request';
import { AppError } from '../middlewares/error.middleware';
import { UserService } from '../services/user.service';
import logger from '../utils/logger';


export class DivinationController {
  
  static async getDivinationInfo(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const userId = req.user?.userId
      const env = req.query.env as string;
      if(!userId) {
        throw new AppError('用户不存在', 404)
      }
      const divinationInfo = await DivinationService.getDivinationInfo(userId, env)
      res.json({
        success: true,
        data: divinationInfo
      });
    } catch (error) {
      next(error);
    }
  }

  static async performDivination(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const userId = req.user?.userId
      const { inputText, birthDate } = req.body
      if(!userId) {
        throw new AppError('用户不存在', 404)
      }
      const text = `问题：${inputText}，生日：${birthDate}`;
      const divinationResult = await DivinationService.performDivination(userId, text)
      try{
            // 更新用户生日
            UserService.updateUserProfile(userId, {
                  birthDate: new Date(birthDate)
            })
      } catch(error){
            logger.error(`performDivination 更新用户生日失败: ${error}`);
      }
      res.json({
        success: true,
        data: divinationResult
      });
    } catch (error) {
      next(error);
    }
  }
  
}