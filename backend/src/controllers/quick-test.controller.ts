import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth.service';
import { AppError } from '../middlewares/error.middleware';
import { WechatService } from '../services/wechat.service';
import { v4 as uuidv4 } from 'uuid';
import logger from '../utils/logger';

export class QuickTestController {
  static async streamTtsReal(req: Request, res: Response, next: NextFunction) {
    const sampleRate = 44100;
    const bitDepth = 16;
    const channels = 1;
    const durationMs = 15000; // 15 秒音频
    const chunkDurationMs = 100; // 每 chunk 100ms

    // 计算总数据大小
    const totalSamples = Math.floor((durationMs / 1000) * sampleRate);
    const dataSize = totalSamples * (bitDepth / 8) * channels;

    // 设置响应头
    res.setHeader('Content-Type', 'audio/wav');
    res.setHeader('Transfer-Encoding', 'chunked');

    // 1. 发送 WAV 文件头
    const wavHeader = QuickTestController.createWavHeader(sampleRate, bitDepth, channels, dataSize);
    res.write(wavHeader);

    // 2. 逐 chunk 生成并发送 PCM 数据
    const chunkSamples = Math.floor((chunkDurationMs / 1000) * sampleRate);
    const totalChunks = Math.ceil(totalSamples / chunkSamples);

    let chunksSent = 0;
    const interval = setInterval(() => {
        if (chunksSent >= totalChunks) {
            clearInterval(interval);
            res.end(); // 结束流
            return;
        }

        // 生成当前 chunk 的正弦波（频率可调模拟不同音高）
        const frequency = 440 + (chunksSent % 5) * 100; // 频率变化（模拟语调）
        const pcmChunk = QuickTestController.generateSineWavePCM(chunkDurationMs, frequency, sampleRate);
        res.write(pcmChunk); // 发送 PCM 数据

        logger.info(`Sent chunk ${chunksSent + 1}/${totalChunks}`);
        chunksSent++;
    }, 50); // 控制发送速度（模拟 AI 生成延迟）
  }

  static generateSineWavePCM(durationMs:number, frequency = 440, sampleRate = 44100) {
    const numSamples = Math.floor((durationMs / 1000) * sampleRate);
    const buffer = Buffer.alloc(numSamples * 2); // 16-bit PCM
    const amplitude = 32760; // 16-bit 最大值（避免爆音）

    for (let i = 0; i < numSamples; i++) {
        const time = i / sampleRate;
        const value = Math.sin(2 * Math.PI * frequency * time); // 正弦波公式
        const pcmValue = Math.floor(amplitude * value);
        buffer.writeInt16LE(pcmValue, i * 2); // 写入 16-bit 小端序
    }
    return buffer;
  }

  // 创建 WAV 文件头
  static createWavHeader(sampleRate:number, bitDepth:number, channels:number, dataSize:number ) {
    const blockAlign = channels * (bitDepth / 8);
    const byteRate = sampleRate * blockAlign;

    const buffer = Buffer.alloc(44);
    buffer.write('RIFF', 0);
    buffer.writeUInt32LE(36 + dataSize, 4); // 文件总大小
    buffer.write('WAVE', 8);
    buffer.write('fmt ', 12);
    buffer.writeUInt32LE(16, 16); // fmt 块大小
    buffer.writeUInt16LE(1, 20); // PCM 格式
    buffer.writeUInt16LE(channels, 22);
    buffer.writeUInt32LE(sampleRate, 24);
    buffer.writeUInt32LE(byteRate, 28);
    buffer.writeUInt16LE(blockAlign, 32);
    buffer.writeUInt16LE(bitDepth, 34);
    buffer.write('data', 36);
    buffer.writeUInt32LE(dataSize, 40); // 数据大小
    return buffer;
  }

  /**
   * 测试获取微信access_token
   */
  static async testGetToken(req: Request, res: Response, next: NextFunction) {
    try {
      const token = await WechatService.getAccessToken();
      res.json({
        data:{
          token,
          message: '获取access_token成功'
        }
      });
      return;
    } catch (error) {
      res.status(500).json({
        message: '获取access_token失败',
        error: error
      });
    }
  }

  /**
   * 测试语音识别流程
   */
  static async testVoiceRecognition(req: Request, res: Response, next: NextFunction) {
    try {
      // 1. 检查是否有文件上传
      if (!req.file) {
        logger.info('请上传语音文件');
        res.status(400).json({
          message: '请上传语音文件'
        });
      }else{
        // 2. 生成唯一的voice_id
        const voiceId = uuidv4();
        logger.info('voiceId', voiceId);

        // 3. 提交语音进行识别
        await WechatService.submitVoiceForRecognition(
          req.file.buffer,
          voiceId,
          'mp3', // 假设上传的是mp3文件
          'zh_CN'
        );

        // 4. 立即查询结果（微信会在10s内处理完成）
        try {
          const result = await WechatService.queryVoiceRecognitionResult(voiceId);
          logger.info('queryVoiceRecognitionResult result', result);
          res.json({
            data: {
              voiceId,
              result,
              message: '语音识别成功'
            }
          });
        } catch (error) {
          // 如果第一次查询失败，等待1秒后重试
          await new Promise(resolve => setTimeout(resolve, 1000));
          const result = await WechatService.queryVoiceRecognitionResult(voiceId);
          res.json({
            data: {
              voiceId,
              result,
              message: '语音识别成功（重试）'
            }
          });
          return;
        }
      }

    } catch (error) {
      logger.error('语音识别测试失败:', error);
      res.status(500).json({
        message: '语音识别失败',
        error: error
      });
    }
  }
}