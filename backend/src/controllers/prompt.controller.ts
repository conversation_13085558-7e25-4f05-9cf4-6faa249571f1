import { Response, NextFunction } from 'express';
import { AuthRequest } from '../types';

export class PromptController {
  
  static async recommend(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const randomList = [
        "上班压力太大了，好累",
        "什么时候才能找到合适的恋人",
        "生活总是忙忙碌碌，什么时候才能停下来？",
        "朋友们都在谈恋爱，我却依然单身",
        "工作中的烦恼让我失去了对生活的热情",
        "有时候觉得自己像个孤独的旅人，迷失方向",
        "生活的琐事让我喘不过气，想要逃离",
        "每天都在期待，期待一个温暖的拥抱",
        "未来的路该如何走，我感到无比迷茫",
        "生活的压力让我感到窒息，渴望自由",
        "每天都在努力，却总觉得不够。",
        "有时候想要逃离这繁忙的城市。",
        "生活的琐事让我感到无奈。",
        "期待能有一个人理解我的心情。",
        "朋友的成功让我感到压力倍增。",
        "生活中总是充满了未知的挑战。",
        "有时候只想静静地待在一个角落。",
        "未来的路上，我希望能有更多的选择。",
        "每天都在思考，怎样才能更快乐。",
        "渴望一段简单而美好的爱情。"
      ];
      res.json({
        success: true,
        data: randomList,
      });
    } catch (error) {
      next(error);
    }
  }

}