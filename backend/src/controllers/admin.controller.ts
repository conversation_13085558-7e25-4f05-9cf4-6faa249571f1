import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';
import { OssService } from '../services/oss.service';
import { ImageProcessingService } from '../services/image-processing.service';
import { verifyAdminCredentials, generateAdminToken } from '../middlewares/auth.admin.middleware';
import { AppError } from '../middlewares/error.middleware';
import { SystemStatusService } from '../services/system-status.service';
import { UserService } from '../services/user.service';
import { UserRewardService } from '../services/user-reward.service';

export class AdminController {

  /**
   * 管理员登录
   */
  static async login(req: Request, res: Response, next: NextFunction) {
    try {
      const { username, password } = req.body;

      // 验证参数
      if (!username || !password) {
        throw new AppError('用户名和密码不能为空', 400);
      }

      // 验证用户名密码
      if (!verifyAdminCredentials(username, password)) {
        throw new AppError('用户名或密码错误', 401);
      }

      // 生成JWT token
      const token = generateAdminToken(username);

      logger.info(`[Admin] User ${username} logged in successfully`);

      res.json({
        success: true,
        message: '登录成功',
        data: {
          token,
          username,
          role: 'admin'
        }
      });
    } catch (error) {
      logger.error('[Admin] Login error:', error);
      next(error);
    }
  }

  /**
   * 验证token有效性
   */
  static async verifyToken(req: Request, res: Response, next: NextFunction) {
    try {
      // 如果能走到这里，说明token已经通过了中间件验证
      const admin = (req as any).admin;

      res.json({
        success: true,
        message: 'Token有效',
        data: {
          username: admin.username,
          role: admin.role
        }
      });
    } catch (error) {
      logger.error('[Admin] Token verification error:', error);
      next(error);
    }
  }

  static async updateResource(req: Request, res: Response, next: NextFunction) {
    const { pageName } = req.params;
    const { config } = req.body; // 前端发送的完整配置内容（JSON字符串）
    const file = req.file; // multer处理的上传文件

    try {
      // 验证必要参数
      if (!file) {
        res.status(400).json({
          success: false,
          message: '未找到上传的文件'
        });
        return;
      }

      if (!config) {
        res.status(400).json({
          success: false,
          message: '未提供配置内容'
        });
        return;
      }

      // 验证配置内容是有效的JSON
      let configObject;
      try {
        configObject = JSON.parse(config);
      } catch (e) {
        res.status(400).json({
          success: false,
          message: '配置内容不是有效的JSON格式'
        });
        return;
      }

      logger.info(`[Admin] Updating resource for ${pageName}, file: ${file.originalname}, size: ${file.size} bytes`);

      // 1. 获取旧配置，用于比较需要删除的文件
      const configObjectName = `assets/pages/${pageName}/config.json`;
      let oldConfigObject = null;
      try {
        const oldConfigContent = await OssService.getObjectContentWithCache(configObjectName, 0); // 不使用缓存，获取最新的
        oldConfigObject = JSON.parse(oldConfigContent);
        logger.info(`[Admin] Retrieved old configuration for comparison`);
      } catch (error) {
        logger.warn(`[Admin] Could not retrieve old configuration (first time setup?):`, error);
      }

      // 2. 从文件名和MIME类型生成OSS路径
      const fileExtension = file.originalname.split('.').pop() || '';
      const fileName = file.originalname;

      // 提取OSS路径 - 假设前端在配置中已经设置了正确的路径
      // 我们可以从配置中找到文件应该上传到的路径
      let ossFilePath = '';

      // 从配置内容中找到包含新文件名的路径
      const configString = JSON.stringify(configObject);
      const fileNameMatch = configString.match(new RegExp(`"([^"]*${fileName}[^"]*)"`, 'g'));

      if (fileNameMatch && fileNameMatch.length > 0) {
        // 取第一个匹配的路径，移除引号
        ossFilePath = fileNameMatch[0].replace(/"/g, '');
      } else {
        // 如果在配置中找不到文件路径，使用默认路径
        ossFilePath = `assets/pages/${pageName}/${fileName}`;
      }

      logger.info(`[Admin] Uploading file to OSS path: ${ossFilePath}`);

      // 3. 比较新旧配置，找出需要删除的文件
      const filesToDelete: string[] = [];
      if (oldConfigObject) {
        const oldFilePaths = AdminController.extractFilePaths(oldConfigObject);
        const newFilePaths = AdminController.extractFilePaths(configObject);

        // 找出在旧配置中存在但在新配置中不存在的文件路径
        filesToDelete.push(...oldFilePaths.filter(path => !newFilePaths.includes(path)));

        if (filesToDelete.length > 0) {
          logger.info(`[Admin] Found ${filesToDelete.length} files to delete:`, filesToDelete);

          // 批量删除不再使用的文件
          await OssService.deleteObjects(filesToDelete);
          logger.info(`[Admin] Successfully deleted ${filesToDelete.length} unused files`);
        } else {
          logger.info(`[Admin] No files need to be deleted`);
        }
      }

      // 4. 上传新文件到OSS
      await OssService.putObjectBuffer(ossFilePath, file.buffer, {
        contentType: file.mimetype
      });

      logger.info(`[Admin] File uploaded successfully to: ${ossFilePath}`);

      // 5. 更新配置文件到OSS
      await OssService.putObjectContent(configObjectName, config);

      logger.info(`[Admin] Configuration updated successfully for: ${configObjectName}`);

      // 6. 触发图片处理（异步）
      try {
        await ImageProcessingService.processImageFile(ossFilePath);
        logger.info(`[Admin] Image processing triggered for: ${ossFilePath}`);
      } catch (error) {
        logger.error(`[Admin] Failed to trigger image processing for: ${ossFilePath}`, error);
        // 不要抛出错误，避免影响主要的上传功能
      }

      // 7. 如果没有启用自动CDN刷新，可以选择手动批量刷新
      // 这里可以根据需要决定是否要批量刷新CDN缓存
      // await OssService.refreshCdnCacheBatch([ossFilePath, configObjectName]);

      res.json({
        success: true,
        message: '资源和配置更新成功',
        data: {
          filePath: ossFilePath,
          configPath: configObjectName,
          deletedFiles: filesToDelete
        }
      });

    } catch (error) {
      logger.error(`[Admin] Error updating resource for ${pageName}:`, error);
      next(error);
    }
  }


  /**
   * 获取指定页面的配置内容 (JSON 字符串)
   */
  static async getConfig(req: Request, res: Response, next: NextFunction) {
    const { pageName } = req.params;
    try {
      logger.info(`[Admin] Fetching config for ${pageName}`);
      const contentString = await OssService.getObjectJsonContentWithCache(pageName);
      res.json({ success: true, data: contentString });
    } catch (error) {
      logger.error(`[Admin] Error fetching config for ${pageName}:`, error);
      next(error);
    }
  }

  /**
   * 更新指定页面的配置内容
   */
  static async updateConfig(req: Request, res: Response, next: NextFunction) {
    const { pageName } = req.params;
    const { content } = req.body; // 前端发送过来的 JSON 字符串

    if (typeof content !== 'string') {
      res.status(400).json({ success: false, message: 'Content must be a string.' });
      return;
    }

    let newConfigObject;
    try {
      // 尝试解析JSON以确保格式基本正确
      newConfigObject = JSON.parse(content);
    } catch (e) {
      res.status(400).json({ success: false, message: 'Invalid JSON format.' });
      return;
    }

    const configObjectName = `assets/pages/${pageName}/config.json`;

    if (!configObjectName) {
      res.status(400).json({ success: false, message: 'Config object name not found for page.' });
      return;
    }

    try {
      logger.info(`[Admin] Updating config for ${configObjectName}`);

      // 获取旧配置，用于比较需要删除的文件
      let oldConfigObject = null;
      const filesToDelete: string[] = [];

      try {
        const oldConfigContent = await OssService.getObjectContentWithCache(configObjectName, 0); // 不使用缓存，获取最新的
        oldConfigObject = JSON.parse(oldConfigContent);
        logger.info(`[Admin] Retrieved old configuration for comparison`);

        // 比较新旧配置，找出需要删除的文件
        const oldFilePaths = AdminController.extractFilePaths(oldConfigObject);
        const newFilePaths = AdminController.extractFilePaths(newConfigObject);

        // 找出在旧配置中存在但在新配置中不存在的文件路径
        filesToDelete.push(...oldFilePaths.filter((path: string) => !newFilePaths.includes(path)));

        if (filesToDelete.length > 0) {
          logger.info(`[Admin] Found ${filesToDelete.length} files to delete:`, filesToDelete);

          // 批量删除不再使用的文件
          await OssService.deleteObjects(filesToDelete);
          logger.info(`[Admin] Successfully deleted ${filesToDelete.length} unused files`);
        } else {
          logger.info(`[Admin] No files need to be deleted`);
        }
      } catch (error) {
        logger.warn(`[Admin] Could not retrieve old configuration for deletion comparison:`, error);
      }

      // 更新配置文件
      await OssService.putObjectContent(configObjectName, content);

      // 触发tap-talk图片预处理（如果更新的是tap-talk配置）
      if (pageName === 'tap-talk') {
        try {
          await ImageProcessingService.preprocessAllTapTalkImages();
          logger.info(`[Admin] Tap-talk image preprocessing triggered after config update`);
        } catch (error) {
          logger.error(`[Admin] Failed to trigger tap-talk image preprocessing:`, error);
          // 不要抛出错误，避免影响主要的更新功能
        }
      }

      res.json({
        success: true,
        message: 'Configuration updated successfully.',
        data: {
          deletedFiles: filesToDelete
        }
      });
    } catch (error) {
      logger.error(`[Admin] Error updating config for ${configObjectName}:`, error);
      next(error);
    }
  }

  /**
   * 获取脚本内容
   */
  static async getScriptContent(req: Request, res: Response, next: NextFunction) {
    const { scriptPath } = req.body;

    try {
      if (!scriptPath || typeof scriptPath !== 'string') {
        res.status(400).json({
          success: false,
          message: '脚本路径不能为空'
        });
        return;
      }

      // 移除开头的斜杠
      const cleanPath = scriptPath.startsWith('/') ? scriptPath.substring(1) : scriptPath;

      logger.info(`[Admin] Fetching script content from: ${cleanPath}`);

      // 从OSS获取脚本内容
      const content = await OssService.getObjectContentWithCache(cleanPath, 300); // 缓存5分钟

      res.json({
        success: true,
        data: content,
        message: '脚本内容获取成功'
      });

    } catch (error) {
      logger.error(`[Admin] Error fetching script content from ${scriptPath}:`, error);

      // 根据错误类型返回不同的错误信息
      if (error && typeof error === 'object' && ('name' in error || 'code' in error)) {
        const errorObj = error as { name?: string; code?: string };
        if (errorObj.name === 'NoSuchKey' || errorObj.code === 'NoSuchKey') {
          res.status(404).json({
            success: false,
            message: '脚本文件不存在'
          });
          return;
        }
      }

      const errorMessage = error instanceof Error ? error.message : '未知错误';
      res.status(500).json({
        success: false,
        message: '获取脚本内容失败: ' + errorMessage
      });
    }
  }

  /**
   * 从配置对象中提取所有文件路径
   * @param configObject 配置对象
   * @returns 文件路径数组
   */
  private static extractFilePaths(configObject: any): string[] {
    const filePaths: string[] = [];

    if (!configObject || !configObject.tapTalks) {
      return filePaths;
    }

    // 遍历所有分类
    for (const category of configObject.tapTalks) {
      // 提取音频文件路径
      if (category.audios && Array.isArray(category.audios)) {
        for (const audioPath of category.audios) {
          if (typeof audioPath === 'string' && audioPath.trim()) {
            filePaths.push(audioPath.trim());
          }
        }
      }

      // 提取项目中的文件路径
      if (category.items && Array.isArray(category.items)) {
        for (const item of category.items) {
          // 提取图片路径
          if (item.image && typeof item.image === 'string' && item.image.trim()) {
            filePaths.push(item.image.trim());
          }

          // 提取详情背景路径
          if (item.detailImage && typeof item.detailImage === 'string' && item.detailImage.trim()) {
            filePaths.push(item.detailImage.trim());
          }

          // 提取脚本文件路径
          if (item.script && typeof item.script === 'string' && item.script.trim()) {
            filePaths.push(item.script.trim());
          }
        }
      }
    }

    // 去重并过滤空值
    return [...new Set(filePaths)].filter(path => path && path.length > 0);
  }

  /**
   * 获取系统状态
   */
  static async getSystemStatus(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info('[Admin] Getting system status');

      const systemStatus = await SystemStatusService.getSystemStatus();

      res.json({
        success: true,
        data: systemStatus
      });
    } catch (error) {
      logger.error('[Admin] Error getting system status:', error);
      next(error);
    }
  }

  /**
   * 更新系统状态
   */
  static async updateSystemStatus(req: Request, res: Response, next: NextFunction) {
    try {
      const { isAvailable, message } = req.body;

      // 验证参数
      if (typeof isAvailable !== 'boolean') {
        throw new AppError('isAvailable 必须是布尔值', 400);
      }

      if (!isAvailable && (!message || typeof message !== 'string' || message.trim() === '')) {
        throw new AppError('系统不可用时必须提供提示消息', 400);
      }

      logger.info(`[Admin] Updating system status: ${isAvailable ? '可用' : '不可用'}, message: ${message || ''}`);

      const success = await SystemStatusService.setSystemStatus(isAvailable, message);

      if (!success) {
        throw new AppError('更新系统状态失败', 500);
      }

      res.json({
        success: true,
        message: '系统状态更新成功',
        data: {
          isAvailable,
          message: message || ''
        }
      });
    } catch (error) {
      logger.error('[Admin] Error updating system status:', error);
      next(error);
    }
  }

  /**
   * 批量发放用户奖励
   */
  static async grantBatchRewards(req: Request, res: Response, next: NextFunction) {
    try {
      const { rewardTypeKey, phoneNumbers } = req.body;

      // 验证参数
      if (!rewardTypeKey || typeof rewardTypeKey !== 'string') {
        throw new AppError('奖励类型不能为空', 400);
      }

      if (!phoneNumbers || !Array.isArray(phoneNumbers) || phoneNumbers.length === 0) {
        throw new AppError('手机号列表不能为空', 400);
      }

      // 验证奖励类型是否为管理员奖励类型
      const validRewardTypes = ['3_days_reward', '7_days_reward', '10_days_reward', '30_days_reward'];
      if (!validRewardTypes.includes(rewardTypeKey)) {
        throw new AppError('无效的奖励类型', 400);
      }

      logger.info(`[Admin] 开始批量发放奖励: ${rewardTypeKey}, 手机号数量: ${phoneNumbers.length}`);

      // 通过手机号批量查找用户
      const { foundUsers, notFoundPhones } = await UserService.getUsersByPhoneNumbers(phoneNumbers);

      if (foundUsers.length === 0) {
        res.json({
          success: true,
          message: '没有找到任何用户',
          data: {
            successfulPhones: [],
            notFoundPhones,
            totalProcessed: phoneNumbers.length,
            successCount: 0
          }
        });
        return;
      }

      // 批量发放奖励
      const userIds = foundUsers.map(user => user.id);
      const successfulRewards = await UserRewardService.grantRewardBatch(
        userIds,
        rewardTypeKey,
        {
          notes: `管理员批量发放奖励 - ${rewardTypeKey}`
        }
      );

      // 获取成功发放奖励的用户手机号
      const successfulUserIds = new Set(successfulRewards.map(reward => reward.user_id));
      const successfulPhones = foundUsers
        .filter(user => successfulUserIds.has(user.id))
        .map(user => user.phone);

      logger.info(`[Admin] 批量奖励发放完成: 成功${successfulPhones.length}个，未找到${notFoundPhones.length}个`);

      res.json({
        success: true,
        message: '批量奖励发放完成',
        data: {
          successfulPhones,
          notFoundPhones,
          totalProcessed: phoneNumbers.length,
          successCount: successfulPhones.length
        }
      });

    } catch (error) {
      logger.error('[Admin] 批量发放奖励失败:', error);
      next(error);
    }
  }
}
