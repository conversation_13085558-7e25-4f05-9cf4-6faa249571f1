import { Response, NextFunction } from 'express';
import * as userService from '../services/user.service';
import { AuthRequest } from '../types';
import { AppError } from '../middlewares/error.middleware';
import { UserService } from '../services/user.service';
import { OssService } from '../services/oss.service';
import { BindPhoneConfig } from '../types/bind-phone.type';
import { WechatService, WechatPhoneInfo } from '../services/wechat.service';
import { UserRewardService } from '../services/user-reward.service';
import logger from '../utils/logger';
import { InvitationService } from '../services/invitation.service';

class UserController {
    /**
     * @description 获取当前登录用户的个人资料
     * @route GET /api/user/
     * @access Private
     */
    static async getProfile(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {
            const user = req.user;
            const userId = user?.userId;
            if (!userId) {
                throw new AppError('Authentication required', 401);
            }
            const userProfile = await UserService.getUserProfile(userId);
            res.status(200).json({ success: true, data: userProfile });
        } catch (error) {
            next(error);
        }
    };

    /**
   * @description 获取当前登录用户的完整个人资料
   * @route GET /api/users/full
   * @access Private
   */
    static async getFullProfile(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {
            const user = req.user;
            const userId = user?.userId;
            if (!userId) {
                throw new AppError('Authentication required', 401);
            }
            const userFullProfile = await UserService.getUserFullProfile(userId);
            res.status(200).json({ success: true, data: userFullProfile });
        } catch (error) {
            next(error);
        }
    };

    /**
     * @description 更新当前登录用户的个人资料
     * @route PUT /api/user/
     * @access Private
     */
    static async updateProfile(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {
            const user = req.user;
            const userId = user?.userId;
            if (!userId) {
                throw new AppError('Authentication required', 401);
            }

            const { username, avatarKey, phone } = req.body;
            const updateData = { username, avatarKey, phone };

            // 过滤掉 undefined 的字段，避免更新为 null
            const validUpdateData = Object.entries(updateData).reduce((acc, [key, value]) => {
                if (value !== undefined) {
                    (acc as any)[key] = value;
                }
                return acc;
            }, {} as Partial<userService.UserUpdatePayload>);


            if (Object.keys(validUpdateData).length === 0) {
                throw new AppError('No fields provided for update', 400);
            }

            const updatedUser = await UserService.updateUserProfile(userId, validUpdateData);
            res.status(200).json({ success: true, data: updatedUser });
        } catch (error) {
            next(error);
        }
    };

    static async updateOnboarding(req: AuthRequest, res: Response, next: NextFunction) {    
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new AppError('Authentication required', 401);
            }
            const { onboarding } = req.body;
            const updatedUser = await UserService.updateOnboarding(userId, onboarding);
            res.status(200).json({ success: true, data: updatedUser });
        } catch (error) {
            next(error);
        }
    }

    static async bindPhoneInfo(req: AuthRequest, res: Response, next: NextFunction) {
        try {
          const userId = req.user?.userId;
          if (!userId) {
            throw new AppError('Authentication required', 401);
          }
          const user = await UserService.getUserProfile(Number(userId));
          const config = await OssService.getObjectJsonContentWithCache<BindPhoneConfig>('bind-phone');
          config.bindPhoneBackgroundImage = OssService.generateResourceUrl(config.bindPhoneBackgroundImage);
          res.json({
            success: true,
            data: {
              isBind: !!user.phone,
              config
            }
          });
        } catch (error) {
          next(error);
        }
    }

    static async bindPhone(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new AppError('Authentication required', 401);
            }

            const { code } = req.body;
            let phoneNumber: string | undefined;

            if (code) {
                const phoneInfo = await WechatService.getPhoneNumberViaCode(code as string);
                phoneNumber = phoneInfo.purePhoneNumber;
            } else {
                throw new AppError('Missing parameters: "code" are required.', 400);
            }

            if (!phoneNumber) {
                // This case should ideally be caught by errors within WechatService methods
                throw new AppError('Failed to retrieve or decrypt phone number.', 500);
            }

            // Update user profile with the phone number
            await UserService.updateUserProfile(userId, { phone: phoneNumber });

            try{
                // 触发新用户奖励
                await UserRewardService.grantNewUserReward(userId);
            }catch(error){
                logger.error('Failed to grant new user reward:', error);
            }

            try{
                // 触发分享奖励
                const invitations = await InvitationService.getInvitationsByInviteeId(userId)
                if(invitations && invitations.length > 0){
                    // todo should be a transaction
                    await UserRewardService.grantInviteReward(invitations[0].inviterId);
                    await UserRewardService.grantInviteReward(userId);
                }
              }catch(error){
                logger.error('Failed to grant invite reward:', error);
            }

            res.status(200).json({ success: true, message: 'Phone number bound successfully.' });

        } catch (error) {
            next(error);
        }
    }

}

export default UserController;