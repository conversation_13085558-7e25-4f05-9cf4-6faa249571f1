import { Response, NextFunction } from 'express';
import logger from '../utils/logger';
import { AppError } from '../middlewares/error.middleware';
import { AuthRequest } from '../types';
import { OssService } from '../services/oss.service';
import { MembershipPlanService } from '../services/membership-plan.service';
import { MembershipConfig } from '../types/membership.type';
import { MembershipPlanKey } from '../enums';

export class UserMembershipController {
  
  static async info(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        throw new AppError('用户未登录', 401);
      }
      const membershipInfo = await UserMembershipController.getMembershipInfo(userId);
      res.json({
        success: true,
        data: membershipInfo
      });
    } catch (error) {
      logger.error("获取会员信息失败: ", error);
      next(error);
    }
  }

  static async getMembershipInfo(userId: number) {
      const config = await OssService.getObjectJsonContentWithCache<MembershipConfig>('membership');

      const planMap = await MembershipPlanService.getPlanMap();
      config.plans.forEach(plan => {
            const originalPrice = planMap[plan.key as MembershipPlanKey].price;
            plan.price = UserMembershipController.formatPrice(Number(originalPrice));
      });
      config.activedButtonImage = OssService.generateResourceUrl(config.activedButtonImage);
      config.features = config.features.map(feature => {
        feature.icon = OssService.generateResourceUrl(feature.icon);
        return feature;
      });
      config.buttonText = '立即购买';
      
      // todo
      return config;
  }

  /**
   * 格式化价格
   * @param price 原始价格
   * @returns 格式化后的价格
  */
  private static formatPrice(price: number): string {
        // 检查价格是否为整数
        if (price % 1 === 0) {
          return price.toFixed(0);
          } else {
          return price.toFixed(2); // 返回保留两位小数的字符串
        }
  }
  
}