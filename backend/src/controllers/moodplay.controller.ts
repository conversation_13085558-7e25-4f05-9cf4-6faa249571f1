import { Response, NextFunction, Request } from 'express';
import { AuthRequest } from "../types";
import { MoodplayService } from '../services/moodplay.service';



export class MoodplayController {

  static async entry(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { playSessionId } = req.params;
      const result = await  MoodplayService.entry(Number(playSessionId));    
      res.json({
        success: true,
        data: result,
      }); 
    }catch(error){
      next(error);
    }
  }
   
}
