import { Response, NextFunction } from 'express';
import logger from '../utils/logger';
import { AppError } from '../middlewares/error.middleware';
import { AuthRequest } from '../types';
import { MyService } from '../services/my.service';

export class MyController {
  
  static async info(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        throw new AppError('用户未登录', 401);
      }
      const info = await MyService.getInfo(userId);
      res.json({
        success: true,
        data: info
      });
    } catch (error) {
      logger.error("获取我的页面信息失败: ", error);
      next(error);
    }
  }

}