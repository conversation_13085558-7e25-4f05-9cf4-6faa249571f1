import { Response, NextFunction } from 'express';
import { AuthRequest } from '../types';
import { ShareService } from '../services/share.service';
import { AppError } from '../middlewares/error.middleware';
import logger from '../utils/logger';

export class ShareController {
  
  static async shareInfo(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const userId = req.user?.userId;
      // release trial develop
      const env = req.query.env as string;
      if (!userId) {
        throw new AppError('用户未登录', 401);
      }
      logger.info("userId: ", userId)
      const shareInfo = await ShareService.shareInfo(userId, env)
      res.json({
        success: true,
        data: shareInfo
      });
    } catch (error) {
      next(error);
    }
  }

}