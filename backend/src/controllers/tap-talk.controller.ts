import { NextFunction, Request, Response } from "express";
import logger from "../utils/logger";
import { OssService } from "../services/oss.service";
import { ImageProcessingService } from "../services/image-processing.service";
import { TapTalkConfig } from "../types/config.type";
import { TapTalk, TapTalkDetail, TapTalkItem, TapTalkPage } from "../types/tap-talk.type";


export class TapTalkController {
  static async getTapTalkInfo(req: Request, res: Response, next: NextFunction) {
    try {
      const { category, id } = req.params;
      const tapTalkConfig = await OssService.getObjectJsonContentWithCache<TapTalkConfig>('tap-talk');
      const tapTalk = tapTalkConfig.tapTalks.find((tapTalk: TapTalk) => tapTalk.category === category);
      if(!tapTalk){
        res.json({
          success: false,
          message: 'Tap talk not found'
        });
        return;
      }
      const item = tapTalk.items.find((item: TapTalkItem) => item.id === Number(id));
      if(!item){
        res.json({
          success: false,
          message: 'Item not found'
        });
        return;
      }
      item.image = OssService.generateResourceUrl(item.image);
      
      // 优先使用处理后的模糊图片
      const originalDetailImageUrl = OssService.generateResourceUrl(item.detailImage);
      const processedDetailImageUrl = await ImageProcessingService.getProcessedImageUrl(originalDetailImageUrl);
      
      if (processedDetailImageUrl) {
        // 如果处理后的图片存在，直接使用
        item.detailImage = processedDetailImageUrl;
        logger.info(`[getTapTalkInfo] 使用预处理的模糊图片: ${item.name}`);
      } else {
        // 如果处理后的图片不存在，使用原图并异步触发处理
        item.detailImage = originalDetailImageUrl;
        logger.info(`[getTapTalkInfo] 使用原图并异步触发处理: ${item.name}`);
        
        // 异步触发图片处理（不阻塞响应）
        ImageProcessingService.addBlurEffectAsync(originalDetailImageUrl).catch((error) => {
          logger.error(`[getTapTalkInfo] 异步处理图片失败: ${item.name}`, error);
        });
      }
      
      try{
            const scriptStr =  await OssService.getObjectContentWithCache(item.script);
            const scriptJson = JSON.parse(scriptStr) as TapTalkPage[];
            item.scriptContent = scriptJson;
      }catch(error){
        logger.error(`Error getting script for item: ${item.name}`, error);
      }
      const randomIndex = Math.floor(Math.random() * tapTalk.audios.length);
      const audio = OssService.generateResourceUrl(tapTalk.audios[randomIndex]);
      res.json({
        success: true,
        data: {
            name: item.name,
            image: item.detailImage,
            scriptContent: item.scriptContent,
            buttonText: tapTalk.buttonText,
            leaveButtonText: tapTalkConfig.leaveButtonText,
            cancelButtonText: tapTalkConfig.cancelButtonText,
            leavingConfirmText: tapTalkConfig.leavingConfirmText,
            glichyImage: OssService.generateResourceUrl(tapTalkConfig.glichyImage),
            nextIcon: OssService.generateResourceUrl(tapTalkConfig.nextIcon),
            audio: audio,
            audioPlayingIcon: OssService.generateResourceUrl(tapTalkConfig.audioPlayingIcon),
            audioPausedIcon: OssService.generateResourceUrl(tapTalkConfig.audioPausedIcon),
            overlayImage: OssService.generateResourceUrl(tapTalkConfig.overlayImage),
        } as TapTalkDetail
      });
    } catch (error) {
      next(error);
    }
  }
}