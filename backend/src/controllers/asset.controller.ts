import { Request, Response, NextFunction } from 'express';
import { AppError } from '../middlewares/error.middleware';
import { OssService } from '../services/oss.service';
import logger from '../utils/logger';

export class AssetController {
  
  static async getAssets(req: Request, res: Response, next: NextFunction) {
    try {
      const pageName = req.params.pageName;
      
      try {
        const assets = await OssService.getPageAssets(pageName);
        
        res.json({
          success: true,
          data: assets
        });
      } catch (error) {
        logger.error('获取资源失败:', error);
        throw new AppError(`获取资源失败`, 500)
      }
    } catch (error) {
      next(error);
    }
  }
}
