import { UserSettingService } from '../services/user-setting.service';
import { AppError } from '../middlewares/error.middleware';
import { NextFunction, Response } from 'express';
import { AuthRequest } from '../types/request';

export class UserSettingController {
  
  static async updateVoiceSetting(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        throw new AppError('用户未登录', 401);
      }
      const { voiceName } = req.body;
      const userSetting = await UserSettingService.updateVoiceSetting(userId, voiceName);
      res.json({
        success: true, 
        data: userSetting
      });
    } catch (error) {
      next(error);
    }
  }

  static async getUserSetting(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        throw new AppError('用户未登录', 401);
      }
      const userSetting = await UserSettingService.getSetting(userId);
      res.json({
        success: true,
        data: userSetting
      });
    } catch (error) {
      next(error);
    }
  }

}