import { Request, Response, NextFunction } from 'express';
import { AppError } from '../middlewares/error.middleware';
import { GoalService } from '../services/goal.service';

export class GoalController {
  
  static async updateMultipleGoals(req: Request, res: Response, next: NextFunction) {
    try {
      const { playSessionId } = req.params;
      const { selectedGoalIds, goalImageKey } = req.body;
      
      if (!Array.isArray(selectedGoalIds)) {
        throw new AppError('selectedGoalIds必须是数组', 400);
      }

      if(!goalImageKey){
        throw new AppError('goalImageKey不能为空', 400);
      }
      
      const updatedGoals = await GoalService.updateMultipleGoals(
        parseInt(playSessionId, 10),
        selectedGoalIds.map(id => parseInt(id, 10)),
        goalImageKey
      );
      
      res.json({
        success: true,
        data: updatedGoals
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * 获取会话的所有目标
   */
  static async getGoalsByPlaySession(req: Request, res: Response, next: NextFunction) {
    try {
      const { playSessionId } = req.params;
      
      const goals = await GoalService.getGoalsByPlaySession(
        parseInt(playSessionId, 10)
      );
      
      res.json({
        success: true,
        data: goals
      });
    } catch (error) {
      next(error);
    }
  }
}