import { Response, NextFunction } from 'express';
import { AuthRequest } from '../types';
import { AppError } from '../middlewares/error.middleware';
import { FeedbackService } from '../services/feedback.service';
import { CreateFeedbackPayload } from '../types/feedback.type';

export class FeedbackController {
  /**
   * @description 创建用户反馈
   * @route POST /api/feedback
   * @access Private
   */
  static async createFeedback(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        throw new AppError('Authentication required', 401);
      }

      const { rating, content } = req.body;

      if (!rating) {
        throw new AppError('Rating is required', 400);
      }

      const payload: CreateFeedbackPayload = {
        rating: Number(rating),
        content
      };

      const feedback = await FeedbackService.createFeedback(userId, payload);

      res.status(201).json({
        success: true,
        message: 'Feedback created successfully',
        data: feedback
      });
    } catch (error) {
      next(error);
    }
  }
 
}
