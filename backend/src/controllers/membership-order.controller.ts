import { Response, NextFunction } from 'express';
import logger from '../utils/logger';
import { AppError } from '../middlewares/error.middleware';
import { AuthRequest } from '../types';
import { MembershipOrderService } from '../services/membership-order.service';
import { MembershipPlanKey } from '../enums/membership.enum';
import WxPay from 'wechatpay-node-v3';
import fs from 'fs';
export class MembershipOrderController {
  
  static async create(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        throw new AppError('用户未登录', 401);
      }
      const { planKey } = req.body;
      
      // 验证planKey是否为有效的枚举值
      if (!Object.values(MembershipPlanKey).includes(planKey)) {
        throw new AppError('无效的会员计划', 400);
      }
     
      const order = await MembershipOrderService.createOrder(userId, planKey as MembershipPlanKey);

      res.json({
        success: true,
        data: order
      });
    } catch (error) {
      logger.error("创建会员订单失败: ", error);
      next(error);
    }
  }

  static async notify(req: AuthRequest, res: Response, next: NextFunction) {
      try {
            const certPath = './apiclient_cert.pem';
            const keyPath = './apiclient_key.pem';
            
            // 检查证书文件是否存在
            if (!fs.existsSync(certPath)) {
              throw new AppError(`微信支付证书文件不存在: ${certPath}`, 500);
            }
            if (!fs.existsSync(keyPath)) {
              throw new AppError(`微信支付私钥文件不存在: ${keyPath}`, 500);
            }
            
            const pay = new WxPay({
                  appid: process.env.WECHAT_APPID || 'wxcef4c287f6cfbe15',
                  mchid: process.env.WECHAT_MCHID || '1713731070',
                  publicKey: fs.readFileSync(certPath),
                  privateKey: fs.readFileSync(keyPath),
                });
            logger.info(`处理微信支付回调: ${JSON.stringify(req.body)}`);
            const result = pay.decipher_gcm(req.body.resource.ciphertext, req.body.resource.associated_data, req.body.resource.nonce, 'eg1i4hubELpquaFQ2301aX0cb5f6RoLj');
            logger.info(`解密后的数据: ${JSON.stringify(result)}`);
            
            // 处理订单状态更新
            if (result && (result as any).trade_state && (result as any).out_trade_no) {
              await MembershipOrderService.handlePaymentCallback(
                (result as any).trade_state,
                (result as any).out_trade_no,
                (result as any).transaction_id,
                (result as any).success_time
              );
            }
            
            res.json({
                  success: true,
            });
      } catch (error) {
        logger.error("处理微信支付回调失败: ", error);
        next(error);
      }
    }

}