import { Request, Response, NextFunction } from 'express';
import { EmotionService } from '../services/emotion.service';
import { AppError } from '../middlewares/error.middleware';

export class EmotionController {
  
  static async updateMultipleEmotions(req: Request, res: Response, next: NextFunction) {
    try {
      const { playSessionId } = req.params;
      const { selectedEmotionIds } = req.body;
      
      if (!Array.isArray(selectedEmotionIds)) {
        throw new AppError('selectedEmotionIds必须是数组', 400);
      }
      
      const updatedEmotions = await EmotionService.updateMultipleEmotions(
        parseInt(playSessionId, 10),
        selectedEmotionIds.map(id => parseInt(id, 10))
      );
      
      res.json({
        success: true,
        data: updatedEmotions
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * 获取会话的所有情绪
   */
  static async getEmotionsByPlaySession(req: Request, res: Response, next: NextFunction) {
    try {
      const { playSessionId } = req.params;
      
      const emotions = await EmotionService.getEmotionsByPlaySession(
        parseInt(playSessionId, 10)
      );
      
      res.json({
        success: true,
        data: emotions
      });
    } catch (error) {
      next(error);
    }
  }
}