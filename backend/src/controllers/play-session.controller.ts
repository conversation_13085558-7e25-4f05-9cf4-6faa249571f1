import { Response, NextFunction } from 'express';
import { AppError } from "../middlewares/error.middleware";
import { AuthRequest } from "../types";
import { PlaySessionService } from '../services/play-session.service';
import logger from '../utils/logger';
import { ActionService } from '../services/action.service';
import { BridgeConfig } from '../types/config.type';
import { OssService } from '../services/oss.service';
import { BridgeInfo, } from '../types/play-session.type';
import { QuoteService } from '../services/quote.service';
import { PlaySessionStatus } from '../enums';
import { SuggestedGoal } from '../models';
import { UserRewardService } from '../services/user-reward.service';

export class PlaySessionController {

  /**
   * 获取会话的所有情绪
   */
  static async get(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { playSessionId } = req.params;
      const playSession = await PlaySessionService.get(
        parseInt(playSessionId, 10)
      );
      res.json({
        success: true,
        data: playSession
      });
    } catch (error) {
      next(error);
    }
  }
  
  static async submit(req: AuthRequest, res: Response, next: NextFunction) {
      try {
        const user = req.user
        const { text } = req.body;
        if(!user?.userId){
          throw new AppError('用户未登录', 401);
        }
        const result = await PlaySessionService.submit(user.userId, text);
        res.json({
          success: true,
          data: result,
        });
      } catch (error) {
        next(error);
      }
  }

  static async generateEmpathy(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      const { playSessionId } = req.params;
      if(!user?.userId){
        throw new AppError('用户未登录', 401);
      }
      const result = await PlaySessionService.generateEmpathyResponse(user.userId, parseInt(playSessionId, 10));
      if(!result){
        throw new AppError('生成共情响应失败', 500);
      }

      // 提前异步生成行动项
      try{
          ActionService.actions(parseInt(playSessionId, 10))  
      }catch(error){  
          logger.error(`异步生成行动项失败: ${error}`);
      }
        
      // 提前异步生成 quote
      try{
          QuoteService.preGenerateQuote(parseInt(playSessionId, 10));
      }catch(error){  
          logger.error(`异步生成 quote 失败: ${error}`);
      }
      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // static async generateEmpathy(req: AuthRequest, res: Response, next: NextFunction) {
  //   try {
  //       const user = req.user;
  //       const { playSessionId } = req.params;
  //       const { forceRegenerate } = req.query;
  //       logger.debug(`generateEmpathy: ${playSessionId}, forceRegenerate: ${forceRegenerate}`);

  //       if (!user?.userId) {
  //           throw new AppError('用户未登录', 401);
  //       }
  //       if (!playSessionId || isNaN(parseInt(playSessionId, 10))) {
  //           throw new AppError('无效的会话ID', 400);
  //       }

  //       const result = await PlaySessionService.generateEmpathyResponse(
  //           user.userId,
  //           parseInt(playSessionId, 10),
  //           forceRegenerate === 'true'
  //       );
        
  //       // 提前异步生成行动项
  //       try{
  //         ActionService.actions(parseInt(playSessionId, 10))  
  //       }catch(error){  
  //         logger.error(`异步生成行动项失败: ${error}`);
  //       }
        
  //       // 提前异步生成 quote
  //       try{
  //         const { QuoteService } = await import('../services/quote.service');
  //         QuoteService.preGenerateQuote(parseInt(playSessionId, 10));
  //       }catch(error){  
  //         logger.error(`异步生成 quote 失败: ${error}`);
  //       }
        
        
  //       res.json({
  //           success: true,
  //           data: result,
  //       });
  //   } catch (error) {
  //       next(error);
  //   }
  // }

  static async getPlayInfo(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      if(!user?.userId){
        throw new AppError('用户未登录', 401);
      }
      const { playSessionId } = req.params;
      const result = await PlaySessionService.getPlayInfo(
        user.userId,
        parseInt(playSessionId, 10)
      );
      res.json({
        success: true,
        data: result,
      });
    }catch(error){
      next(error);
    }
  }

  // static async getWorkflowStatus(req: AuthRequest, res: Response, next: NextFunction) {
  //   try {
  //       const user = req.user;
  //       const { playSessionId } = req.params;

  //       if (!user?.userId) {
  //           throw new AppError('用户未登录', 401);
  //       }
  //       if (!playSessionId || isNaN(parseInt(playSessionId, 10))) {
  //           throw new AppError('无效的会话ID', 400);
  //       }

  //       const result = await PlaySessionService.getWorkflowStatus(
  //           user.userId,
  //           parseInt(playSessionId, 10)
  //       );

  //       res.json({
  //           success: true,
  //           data: result,
  //       });
  //   } catch (error) {
  //       next(error);
  //   }
  // }

  /**
   * 更新播放会话状态
   */
  static async updateStatus(req: AuthRequest, res: Response, next: NextFunction) {
    try {
        const user = req.user;
        const { playSessionId } = req.params;
        const { status } = req.body;

        if (!user?.userId) {
            throw new AppError('用户未登录', 401);
        }
        if (!playSessionId || isNaN(parseInt(playSessionId, 10))) {
            throw new AppError('无效的会话ID', 400);
        }
        if (!status) {
            throw new AppError('状态参数不能为空', 400);
        }

        const result = await PlaySessionService.updateStatus(
            user.userId,
            parseInt(playSessionId, 10),
            status
        );

        res.json({
            success: true,
            data: result,
        });
    } catch (error) {
        next(error);
    }
  }
  static async finish(req: AuthRequest, res: Response, next: NextFunction) {
    try {
        const user = req.user;
        const { playSessionId } = req.params;

        if (!user?.userId) {
            throw new AppError('用户未登录', 401);
        }
        if (!playSessionId || isNaN(parseInt(playSessionId, 10))) {
            throw new AppError('无效的会话ID', 400);
        }
        
        const result = await PlaySessionService.updateStatus(
            user.userId,
            parseInt(playSessionId, 10),
            PlaySessionStatus.FINISH_MOODPLAY
        );

        const selectedGoals = await SuggestedGoal.findAll({
          where: { playSessionId: playSessionId, isSelected: true }
        });
        if (!selectedGoals || selectedGoals.length === 0) {
            throw new AppError('No selected goal found for this session. Please select a goal first.', 400);
        }
        const primaryGoal = selectedGoals[0];

        const finishedGoals:Array<{ goalKey: string; count: number; firstAchieved: boolean, currentAchieved: boolean }> 
          = await PlaySessionService.getFinishedGoalsCount(user.userId, primaryGoal.goalKey);
        const isAllAchieved = finishedGoals.every(goal => goal.count > 0);
        if(isAllAchieved){
          logger.info(`${user.userId} has achieved all goal!`)
          try{
            UserRewardService.grantAchievementReward(user.userId, 7);
          }catch(err){
            logger.error(`${user.userId} grantAchievementReward error`, err);
          }
        }
        res.json({
            success: true,
            data: result,
        });
    } catch (error) {
        next(error);
    }
  }

  static async getBridgeInfo(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      if(!user?.userId){
        throw new AppError('用户未登录', 401);
      }
      const { playSessionId } = req.params;
      const config = await OssService.getObjectJsonContentWithCache<BridgeConfig>('bridge');
      const playSession = await PlaySessionService.get(parseInt(playSessionId, 10));
      if(!playSession){
        throw new AppError('会话不存在', 404);
      }
      
      let goalKey:string = '';
      const suggestedGoals = playSession.suggestedGoals;
      const selectedGoals = suggestedGoals?.filter(goal => goal.isSelected);
      if(selectedGoals && selectedGoals.length > 0){
        goalKey = selectedGoals[0].goalKey;
      }
      if(!goalKey){
        throw new AppError('未找到目标', 400);
      }
      
      const lottieAnimation1 = config.lottieAnimationTemplate.replace('${goalKey}', `${goalKey}1`);
      const lottieAnimation2 = config.lottieAnimationTemplate.replace('${goalKey}', `${goalKey}2`);

      const result:BridgeInfo = {
        lottieAnimation1: lottieAnimation1,
        lottieAnimation2: lottieAnimation2,
        guideText: config.guideText,
        buttonText: config.buttonText,
      }
      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取共情音频流
   */
  static async getEmpathyAudio(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { playSessionId } = req.params;
      
      if (!playSessionId || isNaN(parseInt(playSessionId, 10))) {
        throw new AppError('无效的会话ID', 400);
      }

      const audioData = await PlaySessionService.getEmpathyAudio(
        parseInt(playSessionId, 10)
      );

      if (!audioData) {
        throw new AppError('音频不存在或已过期', 404);
      }

      // 将base64数据转换为Buffer
      const audioBuffer = Buffer.from(audioData, 'base64');
      
      // 设置响应头
      res.set({
        'Content-Type': 'audio/wav',
        'Content-Length': audioBuffer.length.toString(),
        'Cache-Control': 'no-store',
        'Accept-Ranges': 'bytes'        
      });

      // 返回音频流
      res.send(audioBuffer);
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取工作流状态
   */
  static async getWorkflowStatus(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      const { playSessionId } = req.params;
      
      if (!user?.userId) {
        throw new AppError('用户未登录', 401);
      }
      if (!playSessionId || isNaN(parseInt(playSessionId, 10))) {
        throw new AppError('无效的会话ID', 400);
      }

      const result = await PlaySessionService.getWorkflowStatus(
        user.userId,
        parseInt(playSessionId, 10)
      );

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取工作流音频流
   */
  static async getWorkflowAudio(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { playSessionId } = req.params;
      
      if (!playSessionId || isNaN(parseInt(playSessionId, 10))) {
        throw new AppError('无效的会话ID', 400);
      }

      const audioData = await PlaySessionService.getWorkflowAudio(
        parseInt(playSessionId, 10)
      );

      if (!audioData) {
        throw new AppError('工作流音频不存在或已过期', 404);
      }

      // 将base64数据转换为Buffer
      const audioBuffer = Buffer.from(audioData, 'base64');
      
      // 设置响应头
      res.set({
        'Content-Type': 'audio/wav',
        'Content-Length': audioBuffer.length.toString(),
        'Cache-Control': 'no-store',
        'Accept-Ranges': 'bytes'        
      });

      // 返回音频流
      res.send(audioBuffer);
      
    } catch (error) {
      next(error);
    }
  }
 
}
