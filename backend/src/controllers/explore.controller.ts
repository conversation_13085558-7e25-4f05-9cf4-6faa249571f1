import { Request, Response, NextFunction } from 'express';
import { OssService } from '../services/oss.service';

export class ExploreController {
  
  static async entry(req: Request, res: Response, next: NextFunction) {
    try {
      const assets = await OssService.getPageAssets('explore');
      res.json({
        success: true,
        data: assets
      });
    } catch (error) {
      next(error);
    }
  }
}