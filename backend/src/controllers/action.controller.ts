import { Request, Response, NextFunction } from 'express';
import { ActionService } from '../services/action.service';

export class ActionController {
  
  static async actions(req: Request, res: Response, next: NextFunction) {
    try {
      const { playSessionId } = req.params;
      const actions = await ActionService.actions(Number(playSessionId))
      res.json({
        success: true,
        data: actions
      });
    } catch (error) {
      next(error);
    }
  }
  
}