import { Response, NextFunction } from 'express';
import { QuoteService } from '../services/quote.service';
import { AuthRequest } from '../types';
import { AppError } from '../middlewares/error.middleware';

export class QuoteController {
  
  static async getQuote(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const userId = req.user?.userId;
      const env = req.query.env as string;
      if(!userId){
        throw new AppError('用户未登录', 401);
      }
      const { playSessionId } = req.params; 
      const quote = await QuoteService.getQuoteInfo(userId, Number(playSessionId), env)
      res.json({
        success: true,
        data: quote
      });
    } catch (error) {
      next(error);
    }
  }
}