import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth.service';
import { AppError } from '../middlewares/error.middleware';

export class AuthController {
  static async wechatLogin(req: Request, res: Response, next: NextFunction) {
    try {
      const { code } = req.body;
      
      if (!code) {
        throw new AppError('缺少微信授权码', 400);
      }

      const result = await AuthService.loginWithWechat(code);
      
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
}