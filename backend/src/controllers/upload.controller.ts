import { Request, Response, NextFunction } from 'express';
import { AppError } from '../middlewares/error.middleware';
import crypto from 'crypto';
import { WechatService } from '../services/wechat.service';
import { v4 as uuidv4 } from 'uuid';
import ffmpeg from 'fluent-ffmpeg';
import ffmpegInstaller from '@ffmpeg-installer/ffmpeg';
import { PassThrough } from 'stream';
import logger from '../utils/logger';

ffmpeg.setFfmpegPath(ffmpegInstaller.path);

export class UploadController {
  
  async signature(req: Request, res: Response, next: NextFunction) {
    try {
      // 生成上传策略
      const policy = {
        expiration: new Date(Date.now() + 600000).toISOString(), // 签名有效期10分钟
        conditions: [
          ['content-length-range', 0, 2 * 1024 * 1024], // 限制文件大小为10MB
          ['starts-with', '$key', 'uploads/']  // 限制文件路径前缀
        ]
      };

      const policyString = Buffer.from(JSON.stringify(policy)).toString('base64');
      
      // 计算签名
      const signature = crypto
        .createHmac('sha1', process.env.OSS_ACCESS_KEY_SECRET || '')
        .update(policyString)
        .digest('base64');

      // 生成唯一文件名
      const key = `uploads/${Date.now()}-${Math.random().toString(36).slice(2)}.jpg`;

      // 返回上传所需的信息
      res.status(201).json({
        success: true,
        data: {
          host: `https://${process.env.OSS_BUCKET}.${process.env.OSS_ENDPOINT}`,
          formData: {
            OSSAccessKeyId: process.env.OSS_ACCESS_KEY_ID,
            policy: policyString,
            signature: signature,
            key: key,
            success_action_status: '200'
          }
        },
      });
    } catch (error) {
      logger.error(error)
      next(new AppError('获取上传签名失败', 500));
    }
  }

  private async convertAudio(inputBuffer: Buffer): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const inputStream = require('stream').Readable.from(inputBuffer);
      const chunks: Buffer[] = [];
      const outputStream = new PassThrough();
      
      ffmpeg()
        .input(inputStream)
        // 设置输出格式为 mp3
        .toFormat('mp3')
        // 设置采样率为 16kHz
        .audioFrequency(16000)
        // 设置为单声道
        .audioChannels(1)
        // 设置比特率，确保文件小于 1MB
        .audioBitrate('32k')
        .on('error', reject)
        .pipe(outputStream);

      outputStream.on('data', (chunk: Buffer) => chunks.push(chunk));
      outputStream.on('end', () => {
        const outputBuffer = Buffer.concat(chunks);
        // 检查文件大小是否超过 1MB
        if (outputBuffer.length > 1024 * 1024) {
          reject(new Error('转换后的音频文件大于1MB'));
          return;
        }
        resolve(outputBuffer);
      });
      outputStream.on('error', reject);
    });
  }

  async uploadAudio(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.file) {
        throw new AppError('未接收到音频文件', 400);
      }

      let audioBuffer: Buffer;
      try {
        // 转换音频格式
        audioBuffer = await this.convertAudio(req.file.buffer);
      } catch (error) {
        logger.error('音频转换失败:', error);
        throw new AppError('音频格式转换失败', 400);
      }
      // 生成唯一的voice_id
      const voiceId = uuidv4();
      logger.info('voiceId:', voiceId);

      try {
        // 提交转换后的音频进行识别
        const submitResult = await WechatService.submitVoiceForRecognition(
          audioBuffer,
          voiceId,
          'mp3',
          'zh_CN'
        );
        logger.info("submitResult: ", submitResult)

        // 查询识别结果
        try {
          const result = await WechatService.queryVoiceRecognitionResult(voiceId);
          logger.info('语音识别结果:', result);
          
          res.status(200).json({
            success: true,
            data: {
              voiceId,
              result,
              message: '语音识别成功'
            }
          });
        } catch (queryError) {
          logger.error("查询识别结果失败，等待1秒后重试", queryError)
          // 如果第一次查询失败，等待1秒后重试
          await new Promise(resolve => setTimeout(resolve, 1000));
          const result = await WechatService.queryVoiceRecognitionResult(voiceId);
          
          res.status(200).json({
            success: true,
            data: {
              voiceId,
              result,
              message: '语音识别成功（重试）'
            }
          });
        }
      } catch (recognitionError) {
        logger.error('语音识别失败:', recognitionError);
        throw new AppError('语音识别失败', 500);
      }
    } catch (error) {
      logger.error('处理音频识别请求失败:', error);
      next(error);
    }
  }
}