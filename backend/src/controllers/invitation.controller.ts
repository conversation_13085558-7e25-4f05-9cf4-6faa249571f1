import { Response, NextFunction } from 'express';
import { AuthRequest } from '../types';
import { AppError } from '../middlewares/error.middleware';
import logger from '../utils/logger';
import { InvitationService } from '../services/invitation.service';

export class InvitationController {
  
      static async createInvitation(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new AppError('用户未登录', 401);
            }
            const { inviterId } = req.body;
            const invitation = await InvitationService.createInvitation(Number(inviterId), Number(userId));
            res.json({
                success: true,
                data: invitation
            });
        } catch (error) {
            next(error);
        }
      }

}