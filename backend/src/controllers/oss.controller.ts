import { Request, Response, NextFunction } from 'express';
import { AppError } from "../middlewares/error.middleware";
import { OssService } from '../services/oss.service';
import OSS from 'ali-oss';
import logger from '../utils/logger';

export class OssController {
  
      // 获取私有文件的临时访问URL
  static async getSignedUrl(req: Request, res: Response, next: NextFunction) {
      try {
        const { key } = req.query;
        
        if (!key || typeof key !== 'string') {
          throw new AppError('文件路径不能为空', 400);
        }
  
        // 根据文件扩展名确定处理方式
        const options = OssController.getSignatureOptions(key);
  
        const url = OssService.getClient().signatureUrl(key, options);
  
        res.status(200).json({
          success: true,
          data: { url }
        });
      } catch (error) {
        logger.error(error);
        next(error)
      }
    }
  
    // 批量获取私有文件的临时访问URL
    static async getBatchSignedUrls(req: Request, res: Response, next: NextFunction) {
      try {
        const { keys } = req.body;
        
        if (!keys || !Array.isArray(keys) || keys.length === 0) {
          throw new AppError('文件路径列表不能为空', 400);
        }
  
        // 限制单次请求的最大数量
        const MAX_KEYS = 50;
        if (keys.length > MAX_KEYS) {
          throw new AppError(`单次请求最多支持${MAX_KEYS}个文件`, 400);
        }
  
        // 过滤无效的key
        const validKeys = keys.filter(key => typeof key === 'string' && key.trim());
        
        // 并行处理所有签名
        const expiresInSeconds = 3600; // 1小时有效期
        const expireAt = Date.now() + (expiresInSeconds * 1000);
        
        
        // 根据文件类型生成签名
        const signaturePromises = validKeys.map(key => {
          const options = OssController.getSignatureOptions(key);
          options.expires = expiresInSeconds;
          
          const url = OssService.getClient().signatureUrl(key, options);
          return [key, { url, expireAt }];
        });
        
        // 将结果转换为对象格式
        const result = Object.fromEntries(signaturePromises);
  
        res.status(200).json({
          success: true,
          data: result
        });
      } catch (error) {
        logger.error('批量获取签名URL失败:', error);
        next(error)
      }
    }
  
    // 根据文件类型获取签名选项
    private static getSignatureOptions(key: string): OSS.SignatureUrlOptions {
      const options: OSS.SignatureUrlOptions = {
        expires: 3600
      };
      
      const ext = key.split('.').pop()?.toLowerCase();
      
      // 图片处理
      if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(ext || '')) {
        options.process = 'image/resize,w_800';
      }
      
      return options;
    }

    static async clearObjectContentCache(req: Request, res: Response, next: NextFunction) {
      try {
        const count = OssService.clearAllObjectContentCache();
        res.json({ success: true, message: `已清除 ${count} 个对象内容缓存` });
      } catch (error) {
        logger.error('清除对象内容缓存失败:', error);
        next(error);
      }
    }
  
}
