import express, { Express } from 'express';
import cors from 'cors';
import healthCheckRoutes from './routes/health-check.routes';
import authRoutes from './routes/auth.routes';
import quickTestRoutes from './routes/quick-test.routes';
import uploadRoutes from './routes/upload.routes';
import promptRoutes from './routes/prompt.routes';
import playSessionRoutes from './routes/play-session.routes';
import { errorHandler } from './middlewares/error.middleware';
import { responseWrapper } from './middlewares/response.middleware';
import { systemStatusMiddleware } from './middlewares/system-status.middleware';
import { RedisService } from './services/redis.service';
import { ImageProcessingService } from './services/image-processing.service';
import emotionRoutes from './routes/emotion.routes';
import goalRoutes from './routes/goal.routes';
import actionRoutes from './routes/action.routes';
import assetRoutes from './routes/asset.routes';
import ossRoutes from './routes/oss.routes';
import quoteRoutes from './routes/quote.routes';
import moodplayRoutes from './routes/moodplay.routes';
import exploreRoutes from './routes/explore.routes';
import userRoutes from './routes/user.routes';
import logger from './utils/logger';
import shareRoutes from './routes/share.routes';
import invitationRoutes from './routes/invitation.routes';
import userSettingRoutes from './routes/user-setting.routes';
import adminRoutes from './routes/admin.routes';
import userMembershipRoutes from './routes/user-membership.routes';
import myRoutes from './routes/my.routes';
import configRoutes from './routes/config.routes';
import footprintRoutes from './routes/footprint.routes';
import feedbackRoutes from './routes/feedback.routes';
import tapTalkRoutes from './routes/tap-talk.routes';
import divinationRoutes from './routes/divination.routes';
import membershipOrderRoutes from './routes/membership-order.routes';
import systemStatusRoutes from './routes/system-status.routes';


const app: Express = express();
const port = process.env.PORT || 3000;

// CORS 配置
const allowedOrigins = [
  'http://localhost:5173',  // Vite 默认端口
  'http://localhost:3000',  // React 默认端口
  'http://127.0.0.1:5173',
  'http://127.0.0.1:3000',
  // 测试和生产环境域名
  'https://test.moodplay.top',
  'https://moodplay.top'
].filter((origin): origin is string => Boolean(origin)); // 类型守卫过滤掉 undefined

const corsOptions = {
  origin: allowedOrigins,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'Cache-Control'
  ],
  credentials: true, // 允许携带 cookies
  optionsSuccessStatus: 200 // 某些浏览器对 204 状态码的 preflight 请求处理有问题
};

app.use(cors(corsOptions));

app.use(express.json());

app.use(responseWrapper);

// 注册OSS URL处理中间件 - 应用到所有路由
// app.use(processOssUrls());

// 路由
// 健康检查路由 - 不需要系统状态检查
app.use('/api/health-check', healthCheckRoutes);

// 管理员路由 - 不需要系统状态检查
app.use('/api/admin', adminRoutes);

// 系统状态检查路由 - 不需要系统状态检查
app.use('/api/system-status', systemStatusRoutes);

// 小程序路由 - 需要系统状态检查
app.use('/api/user',  userRoutes);

app.use('/api/quick-test',  quickTestRoutes);

app.use('/api/auth',  authRoutes);

app.use('/api/upload',  uploadRoutes);

app.use('/api/prompt',  promptRoutes);

app.use('/api/play-session',  playSessionRoutes);

app.use('/api/moodplay',  moodplayRoutes);

app.use('/api/emotion',  emotionRoutes);

app.use('/api/goal',  goalRoutes);

app.use('/api/quote',  quoteRoutes);

app.use('/api/action',  actionRoutes);

app.use('/api/asset',  assetRoutes);

app.use('/api/oss',  ossRoutes);

app.use('/api/explore',  exploreRoutes);

app.use('/api/share',  shareRoutes);

app.use('/api/invitation',  invitationRoutes);

app.use('/api/user-setting',  userSettingRoutes);

app.use('/api/user-membership',  userMembershipRoutes);

app.use('/api/my',  myRoutes);

app.use('/api/config',  configRoutes);

app.use('/api/footprint',  footprintRoutes);

app.use('/api/feedback',  feedbackRoutes);

app.use('/api/tap-talk', tapTalkRoutes);

app.use('/api/divination', divinationRoutes);

app.use('/api/membership-order', membershipOrderRoutes);

app.use(errorHandler);

// 确保在应用关闭时优雅地关闭Redis连接
process.on('SIGTERM', async () => {
  await RedisService.closeConnection();
  process.exit(0);
});

process.on('SIGINT', async () => {
  await RedisService.closeConnection();
  process.exit(0);
});

process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  // 记录错误日志 + 告警
  // 注意：此时应用可能处于不稳定状态，建议优雅退出
  // process.exit(1); // 强制退出（依赖进程管理器重启）
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// 不需要显式初始化Redis,因为RedisService使用了单例模式
app.listen(port, async () => {
  logger.info(`Server is running at http://localhost:${port}`);

  // 服务启动后，异步开始预处理tap-talk图片
  try {
    logger.info('开始预处理tap-talk图片...');
    await ImageProcessingService.preprocessAllTapTalkImages();
    logger.info('tap-talk图片预处理启动完成');
  } catch (error) {
    logger.error('预处理tap-talk图片时出错:', error);
    // 不要抛出错误，避免影响服务启动
  }
});