{"name": "moodplay", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"start": "NODE_ENV=production node dist/index.js", "start:dev": "NODE_ENV=development ts-node src/index.ts", "dev": "NODE_ENV=development nodemon --watch src --ext ts,json --exec ts-node src/index.ts", "build": "tsc --outDir dist"}, "dependencies": {"@alicloud/cdn20180510": "^6.0.0", "@alicloud/openapi-client": "^0.4.14", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@types/cors": "^2.8.19", "ali-oss": "^6.22.0", "axios": "^1.7.9", "axios-retry": "^4.5.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "fluent-ffmpeg": "^2.1.3", "ioredis": "4.28.5", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "mysql2": "^3.12.0", "sequelize": "^6.37.7", "sharp": "^0.34.3", "uuid": "^11.1.0", "wechatpay-node-v3": "^2.2.1", "winston": "^3.17.0"}, "devDependencies": {"@types/ali-oss": "^6.16.11", "@types/express": "^5.0.0", "@types/fluent-ffmpeg": "^2.1.27", "@types/ioredis": "4.28.10", "@types/jsonwebtoken": "^9.0.7", "@types/multer": "^1.4.13", "@types/node": "^22.10.2", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "eslint": "^9.17.0", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.7.2"}}